#!/bin/bash

# 🔍 Rebyu Dependencies Checker
# =============================

echo "🔍 Verificando dependencias para Rebyu + Firebase Test Lab"
echo "=========================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to check if command exists
check_command() {
    if command -v "$1" >/dev/null 2>&1; then
        echo -e "✅ ${GREEN}$1${NC} - Disponible"
        return 0
    else
        echo -e "❌ ${RED}$1${NC} - No encontrado"
        return 1
    fi
}

# Function to check file exists
check_file() {
    if [ -f "$1" ]; then
        echo -e "✅ ${GREEN}$1${NC} - Existe"
        return 0
    else
        echo -e "❌ ${RED}$1${NC} - No encontrado"
        return 1
    fi
}

echo ""
echo "📋 DEPENDENCIAS BÁSICAS"
echo "======================="
check_command "node"
check_command "npm"
check_command "curl"

echo ""
echo "🤖 ANDROID DEVELOPMENT"
echo "====================="
check_command "java"
if [ -d "$ANDROID_HOME" ]; then
    echo -e "✅ ${GREEN}ANDROID_HOME${NC} - $ANDROID_HOME"
else
    echo -e "⚠️  ${YELLOW}ANDROID_HOME${NC} - No configurado (puede funcionar sin él)"
fi

echo ""
echo "🔥 FIREBASE TEST LAB"
echo "==================="
check_file "/Users/<USER>/google-cloud-sdk/bin/gcloud"

echo ""
echo "📱 PROYECTO REBYU"
echo "================="
check_file "frontend/package.json"
check_file "frontend/capacitor.config.ts"
check_file "frontend/android/gradlew"

echo ""
echo "🐍 BACKEND PYTHON"
echo "================="
check_command "python3"
check_command "uvicorn"
check_file "main.py"
check_file "requirements.txt"

echo ""
echo "📊 RESUMEN"
echo "=========="

# Check if we can build
if [ -f "frontend/package.json" ] && [ -f "frontend/android/gradlew" ]; then
    echo -e "✅ ${GREEN}Proyecto listo para build${NC}"
else
    echo -e "❌ ${RED}Faltan archivos del proyecto${NC}"
fi

# Check if we can test
if [ -f "/Users/<USER>/google-cloud-sdk/bin/gcloud" ]; then
    echo -e "✅ ${GREEN}Firebase Test Lab configurado${NC}"
else
    echo -e "❌ ${RED}Firebase Test Lab no configurado${NC}"
fi

echo ""
echo "🚀 PRÓXIMOS PASOS"
echo "================="
echo "1. Si todo está ✅, ejecuta: ./build-and-test-rebyu.sh"
echo "2. Para solo construir: ./build-and-test-rebyu.sh build"
echo "3. Para solo test: ./build-and-test-rebyu.sh test"
echo "4. Para ayuda: ./build-and-test-rebyu.sh help"
