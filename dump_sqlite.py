import sqlite3
import json

# Connect to SQLite database
conn = sqlite3.connect('classes.db')
conn.row_factory = sqlite3.Row
cursor = conn.cursor()

# Function to escape single quotes in SQL strings
def escape_sql(text):
    if text is None:
        return "NULL"
    return text.replace("'", "''")

# Open a file to write the SQL statements
with open('neon_db_import.sql', 'w') as f:
    # Write header
    f.write("-- SQL statements to import data from SQLite to Neon DB\n")
    f.write("-- Execute these statements on your Neon DB\n\n")
    
    # Dump classes
    f.write("-- Classes\n")
    cursor.execute("SELECT * FROM classes")
    classes = cursor.fetchall()
    
    for class_item in classes:
        name = escape_sql(class_item['name'])
        description = escape_sql(class_item['description'])
        f.write(f"INSERT INTO classes (name, description) VALUES ('{name}', '{description}');\n")
    
    f.write("\n")
    
    # Dump roadmaps
    f.write("-- Roadmaps\n")
    cursor.execute("SELECT * FROM roadmaps")
    roadmaps = cursor.fetchall()
    
    for roadmap in roadmaps:
        title = escape_sql(roadmap['title'])
        description = escape_sql(roadmap['description'])
        topic = escape_sql(roadmap['topic'])
        f.write(f"INSERT INTO roadmaps (title, description, topic) VALUES ('{title}', '{description}', '{topic}');\n")
    
    f.write("\n")
    
    # Dump questions
    f.write("-- Questions (Grade 1)\n")
    cursor.execute("""
        SELECT q.id, q.content, s.unit_id, u.roadmap_id 
        FROM questions q 
        JOIN subunits s ON q.subunit_id = s.id 
        JOIN units u ON s.unit_id = u.id
    """)
    questions = cursor.fetchall()
    
    for question in questions:
        content = escape_sql(question['content'])
        roadmap_id = question['roadmap_id']
        f.write(f"-- Original Question ID: {question['id']}\n")
        f.write(f"INSERT INTO questions (content, grade, roadmap_id, parent_id) VALUES ('{content}', 1, (SELECT id FROM roadmaps WHERE title = (SELECT title FROM roadmaps WHERE id = {roadmap_id} LIMIT 1) LIMIT 1), NULL);\n")
    
    f.write("\n")
    
    # Dump subquestions (Grade 1)
    f.write("-- Subquestions (Grade 2)\n")
    for question in questions:
        cursor.execute("""
            SELECT * FROM subquestions 
            WHERE question_id = ? AND grade = 1
        """, (question['id'],))
        subquestions_g1 = cursor.fetchall()
        
        for subq_g1 in subquestions_g1:
            content = escape_sql(subq_g1['content'])
            question_content = escape_sql(question['content'])
            f.write(f"-- Original Subquestion ID: {subq_g1['id']}\n")
            f.write(f"INSERT INTO questions (content, grade, roadmap_id, parent_id) VALUES ('{content}', 2, NULL, (SELECT id FROM questions WHERE content = '{question_content}' AND grade = 1 LIMIT 1));\n")
    
    f.write("\n")
    
    # Dump subquestions (Grade 2)
    f.write("-- Subquestions (Grade 3)\n")
    cursor.execute("SELECT * FROM subquestions WHERE grade = 1")
    subquestions_g1 = cursor.fetchall()
    
    for subq_g1 in subquestions_g1:
        cursor.execute("""
            SELECT * FROM subquestions 
            WHERE parent_id = ? AND grade = 2
        """, (subq_g1['id'],))
        subquestions_g2 = cursor.fetchall()
        
        for subq_g2 in subquestions_g2:
            content = escape_sql(subq_g2['content'])
            parent_content = escape_sql(subq_g1['content'])
            f.write(f"-- Original Subquestion ID: {subq_g2['id']}\n")
            f.write(f"INSERT INTO questions (content, grade, roadmap_id, parent_id) VALUES ('{content}', 3, NULL, (SELECT id FROM questions WHERE content = '{parent_content}' AND grade = 2 LIMIT 1));\n")

print("SQL dump completed. The file 'neon_db_import.sql' has been created.")
print("Execute this file on your Neon DB to import the data.")