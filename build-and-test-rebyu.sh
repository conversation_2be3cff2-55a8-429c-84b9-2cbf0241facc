#!/bin/bash

# 🔥 Rebyu App - Build & Firebase Test Lab Script
# ===============================================

set -e  # Exit on any error

GCLOUD_PATH="/Users/<USER>/google-cloud-sdk/bin/gcloud"
PROJECT_ID="mobile-testing-1748195029"
APP_NAME="Rebyu"

echo "🔥 $APP_NAME - Build & Test con Firebase Test Lab"
echo "=================================================="

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check if backend is running
check_backend() {
    if curl -s http://localhost:9000/ > /dev/null; then
        echo "✅ Backend está corriendo en http://localhost:9000"
        return 0
    else
        echo "❌ Backend no está corriendo"
        return 1
    fi
}

# Function to start backend
start_backend() {
    echo "🚀 Iniciando backend..."
    export PATH="/Users/<USER>/Library/Python/3.9/bin:$PATH"
    uvicorn main:app --host 0.0.0.0 --port 9000 --reload &
    BACKEND_PID=$!
    echo "⏳ Esperando que el backend esté listo..."
    sleep 8
    
    if check_backend; then
        echo "✅ Backend iniciado correctamente"
    else
        echo "❌ Error al iniciar el backend"
        exit 1
    fi
}

# Function to build the app
build_app() {
    echo ""
    echo "🏗️  PASO 1: Construyendo la aplicación"
    echo "======================================"
    
    cd frontend
    
    # Install dependencies if needed
    if [ ! -d "node_modules" ]; then
        echo "📦 Instalando dependencias..."
        npm install
    fi
    
    # Build the web app
    echo "🌐 Construyendo aplicación web..."
    npm run build
    
    # Sync with Capacitor
    echo "🔄 Sincronizando con Capacitor..."
    npx cap sync android
    
    cd ..
}

# Function to build Android APK
build_android_apk() {
    echo ""
    echo "🤖 PASO 2: Construyendo APK para Android"
    echo "========================================"
    
    cd frontend/android
    
    # Clean previous builds
    echo "🧹 Limpiando builds anteriores..."
    ./gradlew clean
    
    # Build debug APK
    echo "📱 Construyendo APK debug..."
    ./gradlew assembleDebug
    
    # Check if APK was created
    APK_PATH="app/build/outputs/apk/debug/app-debug.apk"
    if [ -f "$APK_PATH" ]; then
        echo "✅ APK creado exitosamente: $APK_PATH"
        
        # Copy APK to project root for easy access
        cp "$APK_PATH" "../../rebyu-debug.apk"
        echo "📁 APK copiado a: rebyu-debug.apk"
    else
        echo "❌ Error: No se pudo crear el APK"
        exit 1
    fi
    
    cd ../..
}

# Function to run Firebase Test Lab
run_firebase_test() {
    echo ""
    echo "🔥 PASO 3: Ejecutando test en Firebase Test Lab"
    echo "=============================================="
    
    APK_FILE="rebyu-debug.apk"
    
    if [ ! -f "$APK_FILE" ]; then
        echo "❌ Error: No se encontró el APK $APK_FILE"
        exit 1
    fi
    
    echo "📱 Información del test:"
    echo "  App: $APP_NAME"
    echo "  APK: $APK_FILE"
    echo "  Proyecto: $PROJECT_ID"
    echo "  Dispositivo: MediumPhone.arm (Android 30)"
    echo ""
    
    echo "🚀 Iniciando test en la nube..."
    
    $GCLOUD_PATH firebase test android run \
        --type robo \
        --app "$APK_FILE" \
        --device model=MediumPhone.arm,version=30,locale=en,orientation=portrait \
        --timeout 5m \
        --project "$PROJECT_ID" \
        --results-bucket="test-lab-results-rebyu" \
        --results-dir="rebyu-test-$(date +%Y%m%d-%H%M%S)"
}

# Function to show results info
show_results_info() {
    echo ""
    echo "📊 RESULTADOS DEL TEST"
    echo "====================="
    echo "🌐 Ver resultados en:"
    echo "  https://console.firebase.google.com/project/$PROJECT_ID/testlab"
    echo ""
    echo "📁 Los resultados incluyen:"
    echo "  ✅ Screenshots de la app"
    echo "  ✅ Video de la sesión de testing"
    echo "  ✅ Logs detallados"
    echo "  ✅ Reporte de crashes (si los hay)"
    echo "  ✅ Métricas de rendimiento"
    echo ""
}

# Main execution
main() {
    case "$1" in
        "backend")
            echo "🚀 Solo iniciando backend..."
            if ! check_backend; then
                start_backend
            fi
            echo "✅ Backend listo en http://localhost:9000"
            ;;
        "build")
            echo "🏗️  Solo construyendo la app..."
            build_app
            build_android_apk
            echo "✅ Build completado: rebyu-debug.apk"
            ;;
        "test")
            echo "🔥 Solo ejecutando test (APK debe existir)..."
            run_firebase_test
            show_results_info
            ;;
        "full"|"")
            echo "🚀 Proceso completo: Backend + Build + Test"
            
            # Check backend
            if ! check_backend; then
                start_backend
            fi
            
            # Build app
            build_app
            build_android_apk
            
            # Run test
            run_firebase_test
            show_results_info
            
            echo ""
            echo "🎉 ¡Proceso completado exitosamente!"
            echo "🌐 Backend: http://localhost:9000"
            echo "📱 APK: rebyu-debug.apk"
            echo "🔥 Test: Ejecutado en Firebase Test Lab"
            ;;
        "help"|"-h"|"--help")
            echo "Uso: $0 [comando]"
            echo ""
            echo "Comandos disponibles:"
            echo "  full     - Proceso completo (default)"
            echo "  backend  - Solo iniciar backend"
            echo "  build    - Solo construir APK"
            echo "  test     - Solo ejecutar test"
            echo "  help     - Mostrar esta ayuda"
            echo ""
            echo "Ejemplos:"
            echo "  $0           # Proceso completo"
            echo "  $0 build     # Solo construir"
            echo "  $0 test      # Solo test"
            ;;
        *)
            echo "❌ Comando desconocido: $1"
            echo "Usa '$0 help' para ver los comandos disponibles"
            exit 1
            ;;
    esac
}

# Verify we're in the right directory
if [ ! -f "frontend/capacitor.config.ts" ]; then
    echo "❌ Error: Ejecuta este script desde la raíz del proyecto Rebyu"
    echo "📁 Directorio actual: $(pwd)"
    echo "📁 Directorio esperado: /Users/<USER>/Development/rebyiu"
    exit 1
fi

# Run main function with all arguments
main "$@"
