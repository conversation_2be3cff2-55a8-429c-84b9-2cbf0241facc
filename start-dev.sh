#!/bin/bash

echo "🚀 Iniciando Rebyu - Mobile Learning App"
echo "======================================"

# Función para limpiar procesos cuando se termina el script
cleanup() {
    echo "🛑 Deteniendo servicios..."
    docker-compose down
    pkill -f "npm run dev"
    exit 0
}

# Capturar Ctrl+C para limpiar procesos
trap cleanup SIGINT

echo "📦 Iniciando backend (Docker) en puerto 9000..."
docker-compose up rebyu-api -d

echo "⏳ Esperando que el backend esté listo..."
sleep 10

echo "🌐 Iniciando frontend en puerto 5173..."
cd frontend
npm run dev &
FRONTEND_PID=$!

echo ""
echo "✅ Rebyu está funcionando!"
echo "======================================"
echo "🔗 Frontend: http://localhost:5173"
echo "🔗 Backend API: http://localhost:9000"
echo "📱 PWA: Abre el frontend en tu móvil para instalarlo"
echo ""
echo "💡 Capacitor está configurado para Android e iOS"
echo "   - Para Android: cd frontend && npx cap run android"
echo "   - Para iOS: cd frontend && npx cap run ios (requiere Xcode)"
echo ""
echo "🛑 Presiona Ctrl+C para detener todos los servicios"

# Esperar hasta que el usuario presione Ctrl+C
wait $FRONTEND_PID 