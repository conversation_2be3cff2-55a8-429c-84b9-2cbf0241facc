#!/bin/bash

# ☁️ Trigger Cloud Build for Rebyu
# ================================

echo "☁️ Triggering cloud build for Rebyu..."

# Check if we're in a git repository
if [ ! -d ".git" ]; then
    echo "❌ Not in a git repository. Initializing..."
    git init
    git add .
    git commit -m "Initial commit for cloud build"
fi

# Push to trigger GitHub Actions
echo "📤 Pushing to GitHub to trigger cloud build..."
git add .
git commit -m "Trigger cloud build - $(date)" || echo "No changes to commit"
git push origin main || echo "⚠️  Make sure to set up GitHub remote"

echo "✅ Cloud build triggered!"
echo "🌐 Check progress at: https://github.com/YOUR_USERNAME/YOUR_REPO/actions"
