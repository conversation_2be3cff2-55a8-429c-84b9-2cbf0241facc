#!/bin/bash

# ☁️ Rebyu Cloud Development Setup
# ================================

set -e

echo "☁️ Setting up Cloud Development Environment for Rebyu"
echo "===================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Get local IP
get_local_ip() {
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        LOCAL_IP=$(ifconfig | grep "inet " | grep -v 127.0.0.1 | awk '{print $2}' | head -1)
    else
        # Linux
        LOCAL_IP=$(hostname -I | awk '{print $1}')
    fi
    echo "$LOCAL_IP"
}

# Setup EAS Build
setup_eas_build() {
    echo -e "${BLUE}🏗️ Setting up EAS Build...${NC}"
    
    cd frontend
    
    # Check if already configured
    if [ ! -f "app.json" ]; then
        echo -e "${YELLOW}📝 Creating app.json for EAS...${NC}"
        cat > app.json << EOF
{
  "expo": {
    "name": "Rebyu",
    "slug": "rebyu-app",
    "version": "1.0.0",
    "platforms": ["android"],
    "android": {
      "package": "io.rebyu.app"
    }
  }
}
EOF
    fi
    
    echo -e "${GREEN}✅ EAS Build configured${NC}"
    cd ..
}

# Setup ngrok tunnel
setup_ngrok_tunnel() {
    echo -e "${BLUE}🌐 Setting up ngrok tunnel...${NC}"
    
    # Check if ngrok is installed
    if ! command -v ngrok &> /dev/null; then
        echo -e "${YELLOW}📦 Installing ngrok...${NC}"
        if [[ "$OSTYPE" == "darwin"* ]]; then
            brew install ngrok/ngrok/ngrok
        else
            curl -s https://ngrok-agent.s3.amazonaws.com/ngrok.asc | sudo tee /etc/apt/trusted.gpg.d/ngrok.asc >/dev/null
            echo "deb https://ngrok-agent.s3.amazonaws.com buster main" | sudo tee /etc/apt/sources.list.d/ngrok.list
            sudo apt update && sudo apt install ngrok
        fi
    fi
    
    echo -e "${GREEN}✅ ngrok ready${NC}"
}

# Setup development configuration
setup_dev_config() {
    echo -e "${BLUE}🔧 Setting up development configuration...${NC}"
    
    LOCAL_IP=$(get_local_ip)
    echo -e "${YELLOW}📍 Local IP detected: $LOCAL_IP${NC}"
    
    # Update development config with actual IP
    sed -i.bak "s/REPLACE_WITH_LOCAL_IP/$LOCAL_IP/g" frontend/capacitor.config.dev.ts
    
    echo -e "${GREEN}✅ Development config updated${NC}"
}

# Create development scripts
create_dev_scripts() {
    echo -e "${BLUE}📝 Creating development scripts...${NC}"
    
    # Hot reload development script
    cat > start-dev-environment.sh << 'EOF'
#!/bin/bash

# 🔥 Start Rebyu Development Environment
# =====================================

echo "🔥 Starting Rebyu Cloud Development Environment"
echo "=============================================="

# Function to cleanup on exit
cleanup() {
    echo "🛑 Shutting down development environment..."
    kill $(jobs -p) 2>/dev/null
    exit 0
}

trap cleanup SIGINT SIGTERM

# Start backend server
echo "🐍 Starting backend server..."
cd /Users/<USER>/Development/rebyiu
uvicorn main:app --host 0.0.0.0 --port 9000 --reload &
BACKEND_PID=$!

# Wait for backend to start
sleep 3

# Start ngrok tunnel for backend
echo "🌐 Starting ngrok tunnel for backend..."
ngrok http 9000 --log=stdout > ngrok.log 2>&1 &
NGROK_PID=$!

# Wait for ngrok to start and get URL
sleep 5
TUNNEL_URL=$(curl -s http://localhost:4040/api/tunnels | jq -r '.tunnels[0].public_url' 2>/dev/null || echo "")

if [ -n "$TUNNEL_URL" ]; then
    echo "✅ Backend tunnel: $TUNNEL_URL"
    
    # Update Capacitor config with tunnel URL
    sed -i.bak "s|REPLACE_WITH_TUNNEL_URL|$TUNNEL_URL|g" frontend/capacitor.config.dev.ts
else
    echo "⚠️  Could not get tunnel URL, check ngrok.log"
fi

# Start frontend development server
echo "🌐 Starting frontend development server..."
cd frontend
npm run dev &
FRONTEND_PID=$!

echo ""
echo "🚀 Development environment ready!"
echo "================================"
echo "📱 Frontend: http://localhost:4321"
echo "🐍 Backend: http://localhost:9000"
echo "🌐 Public Backend: $TUNNEL_URL"
echo ""
echo "📝 Next steps:"
echo "1. Make code changes in your editor"
echo "2. Changes will auto-reload in browser"
echo "3. Use 'npm run cloud-build' to build APK in cloud"
echo "4. Use 'npm run test-cloud' to test on Firebase Test Lab"
echo ""
echo "Press Ctrl+C to stop all services"

# Wait for all background processes
wait
EOF

    chmod +x start-dev-environment.sh
    
    # Cloud build script
    cat > cloud-build.sh << 'EOF'
#!/bin/bash

# ☁️ Trigger Cloud Build for Rebyu
# ================================

echo "☁️ Triggering cloud build for Rebyu..."

# Check if we're in a git repository
if [ ! -d ".git" ]; then
    echo "❌ Not in a git repository. Initializing..."
    git init
    git add .
    git commit -m "Initial commit for cloud build"
fi

# Push to trigger GitHub Actions
echo "📤 Pushing to GitHub to trigger cloud build..."
git add .
git commit -m "Trigger cloud build - $(date)" || echo "No changes to commit"
git push origin main || echo "⚠️  Make sure to set up GitHub remote"

echo "✅ Cloud build triggered!"
echo "🌐 Check progress at: https://github.com/YOUR_USERNAME/YOUR_REPO/actions"
EOF

    chmod +x cloud-build.sh
    
    echo -e "${GREEN}✅ Development scripts created${NC}"
}

# Update package.json with cloud scripts
update_package_json() {
    echo -e "${BLUE}📦 Updating package.json with cloud scripts...${NC}"
    
    cd frontend
    
    # Add cloud development scripts to package.json
    npm pkg set scripts.cloud-build="cd .. && ./cloud-build.sh"
    npm pkg set scripts.dev-cloud="cd .. && ./start-dev-environment.sh"
    npm pkg set scripts.eas-build="eas build --platform android --profile preview"
    npm pkg set scripts.eas-dev="eas build --platform android --profile development"
    
    cd ..
    
    echo -e "${GREEN}✅ Package.json updated${NC}"
}

# Main setup function
main() {
    echo -e "${BLUE}🚀 Starting cloud development setup...${NC}"
    
    setup_eas_build
    setup_ngrok_tunnel
    setup_dev_config
    create_dev_scripts
    update_package_json
    
    echo ""
    echo -e "${GREEN}🎉 Cloud Development Environment Setup Complete!${NC}"
    echo "=============================================="
    echo ""
    echo -e "${YELLOW}📋 Next Steps:${NC}"
    echo "1. Set up EAS account: eas login"
    echo "2. Configure ngrok: ngrok config add-authtoken YOUR_TOKEN"
    echo "3. Start development: ./start-dev-environment.sh"
    echo "4. Build in cloud: npm run eas-build"
    echo "5. Test on Firebase: Use GitHub Actions or manual upload"
    echo ""
    echo -e "${BLUE}💡 Development Workflow:${NC}"
    echo "• Edit code locally (lightweight)"
    echo "• See changes instantly with hot reload"
    echo "• Build APK in cloud (heavy lifting)"
    echo "• Test on real devices via Firebase Test Lab"
    echo ""
}

# Run main function
main "$@"
