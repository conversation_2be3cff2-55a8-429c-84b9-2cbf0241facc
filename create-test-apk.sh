#!/bin/bash

# Create a simple working APK for Firebase Test Lab demonstration
echo "📱 Creating simple test APK for Firebase Test Lab..."

# Create minimal app structure
mkdir -p test-app/src/main/java/com/rebyu/test
mkdir -p test-app/src/main/res/values
mkdir -p test-app/src/main/res/layout

# Create AndroidManifest.xml
cat > test-app/src/main/AndroidManifest.xml << 'EOF'
<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.rebyu.test"
    android:versionCode="1"
    android:versionName="1.0">
    
    <uses-sdk android:minSdkVersion="21" android:targetSdkVersion="30" />
    
    <application
        android:label="Rebyu Test App"
        android:theme="@android:style/Theme.Material.Light">
        <activity
            android:name=".MainActivity"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
    </application>
</manifest>
EOF

# Create MainActivity.java
cat > test-app/src/main/java/com/rebyu/test/MainActivity.java << 'EOF'
package com.rebyu.test;

import android.app.Activity;
import android.os.Bundle;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Button;
import android.view.View;
import android.widget.Toast;

public class MainActivity extends Activity {
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        LinearLayout layout = new LinearLayout(this);
        layout.setOrientation(LinearLayout.VERTICAL);
        layout.setPadding(50, 50, 50, 50);
        
        TextView title = new TextView(this);
        title.setText("🔥 Rebyu Test App");
        title.setTextSize(24);
        title.setPadding(0, 0, 0, 30);
        
        TextView subtitle = new TextView(this);
        subtitle.setText("Testing Firebase Test Lab");
        subtitle.setTextSize(16);
        subtitle.setPadding(0, 0, 0, 30);
        
        Button button1 = new Button(this);
        button1.setText("Test Button 1");
        button1.setOnClickListener(new View.OnClickListener() {
            public void onClick(View v) {
                Toast.makeText(MainActivity.this, "Button 1 clicked!", Toast.LENGTH_SHORT).show();
            }
        });
        
        Button button2 = new Button(this);
        button2.setText("Test Button 2");
        button2.setOnClickListener(new View.OnClickListener() {
            public void onClick(View v) {
                Toast.makeText(MainActivity.this, "Button 2 clicked!", Toast.LENGTH_SHORT).show();
            }
        });
        
        layout.addView(title);
        layout.addView(subtitle);
        layout.addView(button1);
        layout.addView(button2);
        
        setContentView(layout);
    }
}
EOF

# Create strings.xml
cat > test-app/src/main/res/values/strings.xml << 'EOF'
<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="app_name">Rebyu Test App</string>
</resources>
EOF

echo "✅ Test app structure created"

# Try to build using Android SDK tools if available
ANDROID_HOME="/Users/<USER>/Library/Android/sdk"

if [ -d "$ANDROID_HOME" ]; then
    echo "🔨 Attempting to build APK..."
    
    cd test-app
    
    # Find build tools
    BUILD_TOOLS=$(find "$ANDROID_HOME/build-tools" -maxdepth 1 -type d -name "*.*.*" | sort -V | tail -1)
    
    if [ -n "$BUILD_TOOLS" ]; then
        echo "📋 Using build tools: $BUILD_TOOLS"
        
        # Create build directories
        mkdir -p build/gen build/obj build/apk
        
        # Try to generate R.java
        if "$BUILD_TOOLS/aapt" package -f -m \
            -J build/gen \
            -M src/main/AndroidManifest.xml \
            -S src/main/res \
            -I "$ANDROID_HOME/platforms/android-30/android.jar" 2>/dev/null; then
            
            echo "✅ R.java generated"
            
            # Try to compile
            if javac -d build/obj \
                -classpath "$ANDROID_HOME/platforms/android-30/android.jar" \
                -sourcepath src/main/java:build/gen \
                src/main/java/com/rebyu/test/MainActivity.java \
                build/gen/com/rebyu/test/R.java 2>/dev/null; then
                
                echo "✅ Java compiled"
                
                # Create DEX
                if "$BUILD_TOOLS/dx" --dex \
                    --output=build/apk/classes.dex \
                    build/obj 2>/dev/null; then
                    
                    echo "✅ DEX created"
                    
                    # Package APK
                    if "$BUILD_TOOLS/aapt" package -f \
                        -M src/main/AndroidManifest.xml \
                        -S src/main/res \
                        -I "$ANDROID_HOME/platforms/android-30/android.jar" \
                        -F build/apk/test.unsigned.apk \
                        build/apk 2>/dev/null; then
                        
                        echo "✅ APK packaged"
                        
                        # Add DEX
                        cd build/apk
                        "$BUILD_TOOLS/aapt" add test.unsigned.apk classes.dex 2>/dev/null
                        
                        # Sign APK
                        if [ ! -f ~/.android/debug.keystore ]; then
                            mkdir -p ~/.android
                            keytool -genkey -v \
                                -keystore ~/.android/debug.keystore \
                                -storepass android \
                                -alias androiddebugkey \
                                -keypass android \
                                -keyalg RSA \
                                -keysize 2048 \
                                -validity 10000 \
                                -dname "CN=Android Debug,O=Android,C=US" 2>/dev/null
                        fi
                        
                        jarsigner -verbose \
                            -keystore ~/.android/debug.keystore \
                            -storepass android \
                            -keypass android \
                            test.unsigned.apk \
                            androiddebugkey 2>/dev/null
                        
                        # Align APK
                        "$BUILD_TOOLS/zipalign" -f 4 test.unsigned.apk test-signed.apk 2>/dev/null
                        
                        # Copy to project root
                        cp test-signed.apk ../../../rebyu-test.apk
                        cd ../../..
                        
                        echo "✅ APK created successfully: rebyu-test.apk"
                        ls -la rebyu-test.apk
                        exit 0
                    fi
                fi
            fi
        fi
    fi
fi

echo "⚠️  Could not build APK with SDK tools"
echo "📱 Using alternative method..."

# Create a simple APK using a different approach
# Download a known working APK for testing
curl -s -L -o rebyu-test.apk "https://raw.githubusercontent.com/firebase/quickstart-testing/master/android/app/app-debug.apk" || \
curl -s -L -o rebyu-test.apk "https://github.com/firebase/firebase-android-sdk/raw/master/test-apps/testapp/app-debug.apk" || \
echo "Could not download test APK"

if [ -f rebyu-test.apk ] && [ -s rebyu-test.apk ]; then
    echo "✅ Test APK ready: rebyu-test.apk"
    ls -la rebyu-test.apk
else
    echo "❌ Could not create test APK"
    exit 1
fi
