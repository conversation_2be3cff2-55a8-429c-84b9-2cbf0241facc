#!/bin/bash

# 🚀 Rebyu Cloud Development Master Script
# ========================================

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

echo -e "${PURPLE}🚀 Rebyu Cloud Development Environment${NC}"
echo "====================================="

# Function to check prerequisites
check_prerequisites() {
    echo -e "${BLUE}🔍 Checking prerequisites...${NC}"
    
    local missing=0
    
    # Check EAS CLI
    if ! command -v eas &> /dev/null; then
        echo -e "${RED}❌ EAS CLI not found${NC}"
        missing=1
    else
        echo -e "${GREEN}✅ EAS CLI installed${NC}"
    fi
    
    # Check ngrok
    if ! command -v ngrok &> /dev/null; then
        echo -e "${RED}❌ ngrok not found${NC}"
        missing=1
    else
        echo -e "${GREEN}✅ ngrok installed${NC}"
    fi
    
    # Check gcloud
    if [ ! -f "/Users/<USER>/google-cloud-sdk/bin/gcloud" ]; then
        echo -e "${RED}❌ Google Cloud CLI not found${NC}"
        missing=1
    else
        echo -e "${GREEN}✅ Google Cloud CLI installed${NC}"
    fi
    
    if [ $missing -eq 1 ]; then
        echo -e "${RED}❌ Missing prerequisites. Run setup first.${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✅ All prerequisites met${NC}"
}

# Function to start development environment
start_dev_environment() {
    echo -e "${BLUE}🔥 Starting Cloud Development Environment...${NC}"
    
    # Kill any existing processes
    pkill -f "uvicorn main:app" 2>/dev/null || true
    pkill -f "ngrok http" 2>/dev/null || true
    pkill -f "npm run dev" 2>/dev/null || true
    
    echo -e "${YELLOW}🐍 Starting backend server...${NC}"
    uvicorn main:app --host 0.0.0.0 --port 9000 --reload &
    BACKEND_PID=$!
    
    # Wait for backend
    sleep 3
    
    echo -e "${YELLOW}🌐 Starting ngrok tunnel...${NC}"
    ngrok http 9000 --log=stdout > ngrok.log 2>&1 &
    NGROK_PID=$!
    
    # Wait for ngrok and get URL
    sleep 5
    TUNNEL_URL=""
    for i in {1..10}; do
        TUNNEL_URL=$(curl -s http://localhost:4040/api/tunnels 2>/dev/null | jq -r '.tunnels[0].public_url' 2>/dev/null || echo "")
        if [ -n "$TUNNEL_URL" ] && [ "$TUNNEL_URL" != "null" ]; then
            break
        fi
        sleep 2
    done
    
    if [ -n "$TUNNEL_URL" ] && [ "$TUNNEL_URL" != "null" ]; then
        echo -e "${GREEN}✅ Backend tunnel: $TUNNEL_URL${NC}"
        
        # Update Capacitor config
        if [ -f "frontend/capacitor.config.dev.ts" ]; then
            sed -i.bak "s|REPLACE_WITH_TUNNEL_URL|$TUNNEL_URL|g" frontend/capacitor.config.dev.ts
            echo -e "${GREEN}✅ Capacitor config updated with tunnel URL${NC}"
        fi
    else
        echo -e "${YELLOW}⚠️  Could not get tunnel URL automatically${NC}"
        echo -e "${YELLOW}   Check ngrok.log for details${NC}"
    fi
    
    echo -e "${YELLOW}🌐 Starting frontend development server...${NC}"
    cd frontend
    npm run dev &
    FRONTEND_PID=$!
    cd ..
    
    echo ""
    echo -e "${GREEN}🎉 Development Environment Ready!${NC}"
    echo "================================"
    echo -e "${BLUE}📱 Frontend:${NC} http://localhost:4321"
    echo -e "${BLUE}🐍 Backend:${NC} http://localhost:9000"
    echo -e "${BLUE}🌐 Public Backend:${NC} $TUNNEL_URL"
    echo -e "${BLUE}🔧 ngrok Dashboard:${NC} http://localhost:4040"
    echo ""
    echo -e "${YELLOW}💡 Your local machine is now only running:${NC}"
    echo "   • Text editor (for code changes)"
    echo "   • Local dev server (lightweight)"
    echo "   • Backend API (lightweight)"
    echo ""
    echo -e "${YELLOW}☁️  Heavy work happens in the cloud:${NC}"
    echo "   • APK compilation (EAS Build)"
    echo "   • Device testing (Firebase Test Lab)"
    echo ""
    
    # Create cleanup function
    cleanup() {
        echo -e "\n${YELLOW}🛑 Shutting down development environment...${NC}"
        kill $BACKEND_PID $NGROK_PID $FRONTEND_PID 2>/dev/null || true
        exit 0
    }
    
    trap cleanup SIGINT SIGTERM
    
    echo -e "${PURPLE}Press Ctrl+C to stop all services${NC}"
    wait
}

# Function to build in cloud
cloud_build() {
    echo -e "${BLUE}☁️  Building APK in the cloud...${NC}"
    
    cd frontend
    
    # Check EAS login
    if ! eas whoami &>/dev/null; then
        echo -e "${YELLOW}🔐 Please login to EAS first:${NC}"
        eas login
    fi
    
    echo -e "${YELLOW}🏗️  Starting cloud build...${NC}"
    eas build --platform android --profile preview --non-interactive
    
    echo -e "${GREEN}✅ Cloud build started!${NC}"
    echo -e "${BLUE}🌐 Monitor progress at: https://expo.dev/accounts/[your-account]/projects/rebyu-app/builds${NC}"
    
    cd ..
}

# Function to test on Firebase
test_firebase() {
    echo -e "${BLUE}🔥 Testing on Firebase Test Lab...${NC}"
    
    ./firebase-cloud-test.sh test
}

# Function to show development status
show_status() {
    echo -e "${BLUE}📊 Development Environment Status${NC}"
    echo "================================"
    
    # Check if services are running
    if pgrep -f "uvicorn main:app" > /dev/null; then
        echo -e "${GREEN}✅ Backend server running${NC}"
    else
        echo -e "${RED}❌ Backend server not running${NC}"
    fi
    
    if pgrep -f "ngrok http" > /dev/null; then
        echo -e "${GREEN}✅ ngrok tunnel active${NC}"
        TUNNEL_URL=$(curl -s http://localhost:4040/api/tunnels 2>/dev/null | jq -r '.tunnels[0].public_url' 2>/dev/null || echo "")
        if [ -n "$TUNNEL_URL" ] && [ "$TUNNEL_URL" != "null" ]; then
            echo -e "${BLUE}   Tunnel URL: $TUNNEL_URL${NC}"
        fi
    else
        echo -e "${RED}❌ ngrok tunnel not active${NC}"
    fi
    
    if pgrep -f "npm run dev" > /dev/null; then
        echo -e "${GREEN}✅ Frontend dev server running${NC}"
    else
        echo -e "${RED}❌ Frontend dev server not running${NC}"
    fi
    
    echo ""
    echo -e "${BLUE}🏗️  Recent EAS Builds:${NC}"
    cd frontend
    eas build:list --limit 3 2>/dev/null || echo -e "${YELLOW}   No builds found or not logged in${NC}"
    cd ..
}

# Function to open relevant dashboards
open_dashboards() {
    echo -e "${BLUE}🌐 Opening development dashboards...${NC}"
    
    # Open ngrok dashboard
    open "http://localhost:4040" 2>/dev/null || echo "ngrok dashboard: http://localhost:4040"
    
    # Open Firebase Test Lab
    open "https://console.firebase.google.com/project/mobile-testing-1748195029/testlab" 2>/dev/null || echo "Firebase Test Lab: https://console.firebase.google.com/project/mobile-testing-1748195029/testlab"
    
    # Open EAS dashboard
    open "https://expo.dev" 2>/dev/null || echo "EAS Dashboard: https://expo.dev"
}

# Function to show help
show_help() {
    echo -e "${PURPLE}🚀 Rebyu Cloud Development Commands${NC}"
    echo "=================================="
    echo ""
    echo "Usage: $0 [command]"
    echo ""
    echo -e "${BLUE}Development Commands:${NC}"
    echo "  start          - Start complete development environment"
    echo "  status         - Show current status of all services"
    echo "  stop           - Stop all development services"
    echo "  dashboards     - Open all relevant dashboards"
    echo ""
    echo -e "${BLUE}Build Commands:${NC}"
    echo "  build          - Build APK in the cloud (EAS)"
    echo "  test           - Test latest APK on Firebase Test Lab"
    echo "  build-test     - Build and test in sequence"
    echo ""
    echo -e "${BLUE}Setup Commands:${NC}"
    echo "  check          - Check prerequisites"
    echo "  setup-eas      - Setup EAS account and login"
    echo "  setup-ngrok    - Setup ngrok authentication"
    echo ""
    echo -e "${YELLOW}Examples:${NC}"
    echo "  $0 start       # Start development environment"
    echo "  $0 build       # Build APK in cloud"
    echo "  $0 test        # Test on Firebase Test Lab"
    echo "  $0 status      # Check what's running"
}

# Main function
main() {
    case "$1" in
        "start")
            check_prerequisites
            start_dev_environment
            ;;
        "build")
            cloud_build
            ;;
        "test")
            test_firebase
            ;;
        "build-test")
            cloud_build
            echo -e "${YELLOW}⏳ Waiting for build to complete before testing...${NC}"
            echo -e "${YELLOW}   Download APK when ready and run: $0 test${NC}"
            ;;
        "status")
            show_status
            ;;
        "stop")
            echo -e "${YELLOW}🛑 Stopping all services...${NC}"
            pkill -f "uvicorn main:app" 2>/dev/null || true
            pkill -f "ngrok http" 2>/dev/null || true
            pkill -f "npm run dev" 2>/dev/null || true
            echo -e "${GREEN}✅ All services stopped${NC}"
            ;;
        "dashboards")
            open_dashboards
            ;;
        "check")
            check_prerequisites
            ;;
        "setup-eas")
            echo -e "${BLUE}🔐 Setting up EAS account...${NC}"
            cd frontend
            eas login
            eas build:configure
            cd ..
            ;;
        "setup-ngrok")
            echo -e "${BLUE}🌐 Setting up ngrok...${NC}"
            echo "1. Go to: https://dashboard.ngrok.com/get-started/your-authtoken"
            echo "2. Copy your authtoken"
            echo "3. Run: ngrok config add-authtoken YOUR_TOKEN"
            ;;
        "help"|"-h"|"--help"|"")
            show_help
            ;;
        *)
            echo -e "${RED}❌ Unknown command: $1${NC}"
            echo "Use '$0 help' to see available commands"
            exit 1
            ;;
    esac
}

main "$@"
