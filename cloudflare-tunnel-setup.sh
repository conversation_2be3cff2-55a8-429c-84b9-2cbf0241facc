#!/bin/bash

# ☁️ Cloudflare Tunnel para Backend Rebyu
# =======================================

echo "☁️ Configurando Cloudflare Tunnel para Backend Rebyu"
echo "===================================================="

# Instalar cloudflared
echo "📦 Instalando Cloudflare Tunnel..."
if ! command -v cloudflared &> /dev/null; then
    echo "Descargando cloudflared..."
    curl -L --output cloudflared.pkg https://github.com/cloudflare/cloudflared/releases/latest/download/cloudflared-darwin-amd64.pkg
    sudo installer -pkg cloudflared.pkg -target /
    rm cloudflared.pkg
    echo "✅ cloudflared instalado"
else
    echo "✅ cloudflared ya está instalado"
fi

echo ""
echo "🔧 CONFIGURACIÓN PASO A PASO:"
echo "============================"
echo ""
echo "1️⃣  Autenticar con Cloudflare:"
echo "   cloudflared tunnel login"
echo "   # Se abrirá tu navegador para autorizar"
echo ""
echo "2️⃣  Crear tunnel para Rebyu:"
echo "   cloudflared tunnel create rebyu-backend"
echo ""
echo "3️⃣  Configurar tunnel:"

# Crear archivo de configuración
mkdir -p ~/.cloudflared
cat > ~/.cloudflared/config.yml << 'EOF'
tunnel: rebyu-backend
credentials-file: ~/.cloudflared/rebyu-backend.json

ingress:
  - hostname: rebyu-api.tu-dominio.com
    service: http://localhost:9000
  - service: http_status:404
EOF

echo "   ✅ Archivo de configuración creado en ~/.cloudflared/config.yml"
echo ""
echo "4️⃣  Crear DNS record:"
echo "   cloudflared tunnel route dns rebyu-backend rebyu-api.tu-dominio.com"
echo ""
echo "5️⃣  Iniciar tunnel:"
echo "   cloudflared tunnel run rebyu-backend"
echo ""

echo "🚀 COMANDOS COMPLETOS PARA EJECUTAR:"
echo "==================================="
echo ""
echo "# Paso 1: Autenticar"
echo "cloudflared tunnel login"
echo ""
echo "# Paso 2: Crear tunnel"
echo "cloudflared tunnel create rebyu-backend"
echo ""
echo "# Paso 3: Configurar DNS (reemplaza con tu dominio)"
echo "cloudflared tunnel route dns rebyu-backend rebyu-api.tudominio.com"
echo ""
echo "# Paso 4: Iniciar backend local"
echo "cd /Users/<USER>/Development/rebyiu"
echo "uvicorn main:app --host 0.0.0.0 --port 9000 --reload &"
echo ""
echo "# Paso 5: Iniciar tunnel"
echo "cloudflared tunnel run rebyu-backend"
echo ""

echo "📱 ACTUALIZAR CAPACITOR CONFIG:"
echo "=============================="
echo "Edita: /Users/<USER>/Development/rebyiu/frontend/capacitor.config.ts"
echo ""
cat > /tmp/capacitor-config-example.ts << 'EOF'
import type { CapacitorConfig } from '@capacitor/cli';

const config: CapacitorConfig = {
  appId: 'io.rebyu.app',
  appName: 'Rebyu',
  webDir: 'dist',
  bundledWebRuntime: false,
  server: {
    androidScheme: 'https',
    iosScheme: 'rebyu',
    cleartext: true,
    allowNavigation: [
      'https://rebyu-api.tudominio.com',  // Tu tunnel URL
      'http://localhost:9000'             // Para desarrollo local
    ]
  },
  plugins: {
    StatusBar: {
      style: 'default',
      backgroundColor: '#000000'
    }
  }
};

export default config;
EOF

echo "Ejemplo de configuración guardado en /tmp/capacitor-config-example.ts"
echo ""

echo "🌐 ALTERNATIVA RÁPIDA - TUNNEL TEMPORAL:"
echo "======================================="
echo "Para testing rápido sin configurar dominio:"
echo ""
echo "# Tunnel temporal (sin DNS)"
echo "cloudflared tunnel --url http://localhost:9000"
echo ""
echo "# Te dará una URL como: https://abc123.trycloudflare.com"
echo "# Usa esa URL en tu app móvil"

echo ""
echo "💡 VENTAJAS DE CLOUDFLARE TUNNEL:"
echo "==============================="
echo "✅ Gratis para uso personal"
echo "✅ HTTPS automático"
echo "✅ No necesitas abrir puertos"
echo "✅ Funciona desde cualquier red"
echo "✅ Compatible con Firebase Test Lab"
echo "✅ Estable y confiable"
