import sqlite3
import psycopg2
import os
import json
from psycopg2.extras import RealDictCursor

# Connect to SQLite database
sqlite_conn = sqlite3.connect('classes.db')
sqlite_conn.row_factory = sqlite3.Row
sqlite_cursor = sqlite_conn.cursor()

# Get Neon DB connection string from environment variable
neon_db_url = os.getenv("DATABASE_URL", "postgresql://default:<EMAIL>/classes_db?sslmode=require")

# Connect to Neon DB
try:
    neon_conn = psycopg2.connect(neon_db_url)
    neon_conn.autocommit = True
    neon_cursor = neon_conn.cursor(cursor_factory=RealDictCursor)
    print("Connected to Neon DB successfully!")
except Exception as e:
    print(f"Error connecting to Neon DB: {e}")
    exit(1)

# Function to migrate classes
def migrate_classes():
    print("Migrating classes...")
    sqlite_cursor.execute("SELECT * FROM classes")
    classes = sqlite_cursor.fetchall()
    
    for class_item in classes:
        try:
            # Check if class already exists
            neon_cursor.execute("SELECT id FROM classes WHERE name = %s", (class_item['name'],))
            existing_class = neon_cursor.fetchone()
            
            if existing_class:
                print(f"Class '{class_item['name']}' already exists with ID {existing_class['id']}")
                continue
            
            # Insert class
            neon_cursor.execute(
                "INSERT INTO classes (name, description) VALUES (%s, %s) RETURNING id",
                (class_item['name'], class_item['description'])
            )
            new_class = neon_cursor.fetchone()
            print(f"Class '{class_item['name']}' migrated with ID {new_class['id']}")
        except Exception as e:
            print(f"Error migrating class '{class_item['name']}': {e}")

# Function to migrate roadmaps
def migrate_roadmaps():
    print("Migrating roadmaps...")
    sqlite_cursor.execute("SELECT * FROM roadmaps")
    roadmaps = sqlite_cursor.fetchall()
    
    # Create a mapping from old roadmap IDs to new roadmap IDs
    roadmap_id_map = {}
    
    for roadmap in roadmaps:
        try:
            # Check if roadmap already exists
            neon_cursor.execute(
                "SELECT id FROM roadmaps WHERE title = %s AND topic = %s",
                (roadmap['title'], roadmap['topic'])
            )
            existing_roadmap = neon_cursor.fetchone()
            
            if existing_roadmap:
                print(f"Roadmap '{roadmap['title']}' already exists with ID {existing_roadmap['id']}")
                roadmap_id_map[roadmap['id']] = existing_roadmap['id']
                continue
            
            # Insert roadmap
            neon_cursor.execute(
                "INSERT INTO roadmaps (title, description, topic) VALUES (%s, %s, %s) RETURNING id",
                (roadmap['title'], roadmap['description'], roadmap['topic'])
            )
            new_roadmap = neon_cursor.fetchone()
            roadmap_id_map[roadmap['id']] = new_roadmap['id']
            print(f"Roadmap '{roadmap['title']}' migrated with ID {new_roadmap['id']}")
        except Exception as e:
            print(f"Error migrating roadmap '{roadmap['title']}': {e}")
    
    return roadmap_id_map

# Function to migrate questions
def migrate_questions(roadmap_id_map):
    print("Migrating questions...")
    
    # Get all questions from SQLite
    sqlite_cursor.execute("""
        SELECT q.id, q.content, s.unit_id, u.roadmap_id 
        FROM questions q 
        JOIN subunits s ON q.subunit_id = s.id 
        JOIN units u ON s.unit_id = u.id
    """)
    questions = sqlite_cursor.fetchall()
    
    # Create a mapping from old question IDs to new question IDs
    question_id_map = {}
    
    for question in questions:
        try:
            old_roadmap_id = question['roadmap_id']
            new_roadmap_id = roadmap_id_map.get(old_roadmap_id)
            
            if not new_roadmap_id:
                print(f"Could not find new roadmap ID for old roadmap ID: {old_roadmap_id}")
                continue
            
            # Insert question (grade 1)
            neon_cursor.execute(
                "INSERT INTO questions (content, grade, roadmap_id, parent_id) VALUES (%s, %s, %s, %s) RETURNING id",
                (question['content'], 1, new_roadmap_id, None)
            )
            new_question = neon_cursor.fetchone()
            question_id_map[question['id']] = new_question['id']
            print(f"Question '{question['content']}' migrated with ID {new_question['id']}")
            
            # Get subquestions for this question
            sqlite_cursor.execute("""
                SELECT * FROM subquestions 
                WHERE question_id = ? AND grade = 1
            """, (question['id'],))
            subquestions_g1 = sqlite_cursor.fetchall()
            
            for subq_g1 in subquestions_g1:
                try:
                    # Insert subquestion (grade 2)
                    neon_cursor.execute(
                        "INSERT INTO questions (content, grade, roadmap_id, parent_id) VALUES (%s, %s, %s, %s) RETURNING id",
                        (subq_g1['content'], 2, None, new_question['id'])
                    )
                    new_subq_g1 = neon_cursor.fetchone()
                    print(f"Subquestion (grade 2) '{subq_g1['content']}' migrated with ID {new_subq_g1['id']}")
                    
                    # Get grade 2 subquestions
                    sqlite_cursor.execute("""
                        SELECT * FROM subquestions 
                        WHERE parent_id = ? AND grade = 2
                    """, (subq_g1['id'],))
                    subquestions_g2 = sqlite_cursor.fetchall()
                    
                    for subq_g2 in subquestions_g2:
                        try:
                            # Insert subquestion (grade 3)
                            neon_cursor.execute(
                                "INSERT INTO questions (content, grade, roadmap_id, parent_id) VALUES (%s, %s, %s, %s) RETURNING id",
                                (subq_g2['content'], 3, None, new_subq_g1['id'])
                            )
                            new_subq_g2 = neon_cursor.fetchone()
                            print(f"Subquestion (grade 3) '{subq_g2['content']}' migrated with ID {new_subq_g2['id']}")
                        except Exception as e:
                            print(f"Error migrating subquestion (grade 3) '{subq_g2['content']}': {e}")
                except Exception as e:
                    print(f"Error migrating subquestion (grade 2) '{subq_g1['content']}': {e}")
        except Exception as e:
            print(f"Error migrating question '{question['content']}': {e}")
    
    return question_id_map

# Main migration function
def migrate_data():
    print("Starting direct data migration from SQLite to Neon DB...")
    
    # Migrate classes
    migrate_classes()
    
    # Migrate roadmaps
    roadmap_id_map = migrate_roadmaps()
    
    # Migrate questions
    question_id_map = migrate_questions(roadmap_id_map)
    
    print("Data migration completed.")

if __name__ == "__main__":
    migrate_data()
    
    # Close connections
    sqlite_conn.close()
    neon_conn.close()