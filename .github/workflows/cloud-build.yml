name: 🚀 Rebyu Cloud Build & Test

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]
  workflow_dispatch:
    inputs:
      build_type:
        description: 'Build type'
        required: true
        default: 'preview'
        type: choice
        options:
        - preview
        - development
        - production

jobs:
  build-and-test:
    name: 🏗️ Build APK & Test on Firebase
    runs-on: ubuntu-latest
    
    steps:
    - name: 📥 Checkout repository
      uses: actions/checkout@v4
      
    - name: 🟢 Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json
        
    - name: ☕ Setup Java
      uses: actions/setup-java@v4
      with:
        distribution: 'temurin'
        java-version: '11'
        
    - name: 🤖 Setup Android SDK
      uses: android-actions/setup-android@v3
      
    - name: 📦 Install dependencies
      run: |
        cd frontend
        npm ci
        
    - name: 🏗️ Build web app
      run: |
        cd frontend
        npm run build
        
    - name: 🔄 Sync Capacitor
      run: |
        cd frontend
        npx cap sync android
        
    - name: 🔧 Make gradlew executable
      run: chmod +x frontend/android/gradlew
      
    - name: 📱 Build Android APK
      run: |
        cd frontend/android
        ./gradlew assembleDebug --no-daemon --stacktrace
        
    - name: 📤 Upload APK artifact
      uses: actions/upload-artifact@v4
      with:
        name: rebyu-debug-apk-${{ github.sha }}
        path: frontend/android/app/build/outputs/apk/debug/app-debug.apk
        retention-days: 30
        
    - name: 🔥 Test on Firebase Test Lab
      if: github.event_name == 'push' || github.event_name == 'workflow_dispatch'
      env:
        GOOGLE_APPLICATION_CREDENTIALS_JSON: ${{ secrets.FIREBASE_SERVICE_ACCOUNT }}
      run: |
        # Setup Google Cloud CLI
        echo "$GOOGLE_APPLICATION_CREDENTIALS_JSON" > /tmp/service-account.json
        export GOOGLE_APPLICATION_CREDENTIALS=/tmp/service-account.json
        
        # Install gcloud CLI
        curl https://sdk.cloud.google.com | bash
        source $HOME/google-cloud-sdk/path.bash.inc
        
        # Authenticate and set project
        gcloud auth activate-service-account --key-file=/tmp/service-account.json
        gcloud config set project mobile-testing-**********
        
        # Run Firebase Test Lab
        gcloud firebase test android run \
          --type robo \
          --app frontend/android/app/build/outputs/apk/debug/app-debug.apk \
          --device model=MediumPhone.arm,version=30,locale=en,orientation=portrait \
          --timeout 5m \
          --results-bucket=rebyu-test-results \
          --results-dir=test-$(date +%Y%m%d-%H%M%S)
          
    - name: 📊 Comment PR with results
      if: github.event_name == 'pull_request'
      uses: actions/github-script@v7
      with:
        script: |
          github.rest.issues.createComment({
            issue_number: context.issue.number,
            owner: context.repo.owner,
            repo: context.repo.repo,
            body: '🚀 **Cloud Build Complete!**\n\n✅ APK built successfully in the cloud\n📱 Ready for Firebase Test Lab testing\n📥 Download APK from Actions artifacts'
          })
