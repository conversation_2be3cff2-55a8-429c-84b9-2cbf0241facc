name: 🚀 Rebyu Cloud Build & Test

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]
  workflow_dispatch:
    inputs:
      build_type:
        description: 'Build type'
        required: true
        default: 'debug'
        type: choice
        options:
        - debug
        - release

jobs:
  build-and-test:
    name: 🏗️ Build APK & Test on Firebase
    runs-on: ubuntu-latest

    steps:
    - name: 📥 Checkout repository
      uses: actions/checkout@v4

    - name: 🟢 Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json

    - name: ☕ Setup Java
      uses: actions/setup-java@v4
      with:
        distribution: 'temurin'
        java-version: '11'

    - name: 🤖 Setup Android SDK
      uses: android-actions/setup-android@v3

    - name: 📦 Install dependencies
      run: |
        cd frontend
        npm ci

    - name: 🏗️ Build web app
      run: |
        cd frontend
        npm run build

    - name: 🔄 Sync Capacitor
      run: |
        cd frontend
        npx cap sync android

    - name: 🔧 Fix Gradle compatibility
      run: |
        cd frontend/android
        # Update Gradle wrapper to compatible version
        sed -i 's/gradle-8.5-all.zip/gradle-7.6-all.zip/g' gradle/wrapper/gradle-wrapper.properties
        # Update Android Gradle Plugin
        sed -i 's/8.0.0/7.4.2/g' build.gradle
        # Update compile SDK
        sed -i 's/compileSdkVersion = 34/compileSdkVersion = 33/g' variables.gradle
        sed -i 's/targetSdkVersion = 34/targetSdkVersion = 33/g' variables.gradle

    - name: 🔧 Make gradlew executable
      run: chmod +x frontend/android/gradlew

    - name: 📱 Build Android APK
      run: |
        cd frontend/android
        ./gradlew clean
        ./gradlew assembleDebug --no-daemon --stacktrace

    - name: 📤 Upload APK artifact
      uses: actions/upload-artifact@v4
      with:
        name: rebyu-debug-apk-${{ github.sha }}
        path: frontend/android/app/build/outputs/apk/debug/app-debug.apk
        retention-days: 30

    - name: 📊 Comment with download link
      if: github.event_name == 'push' || github.event_name == 'workflow_dispatch'
      run: |
        echo "🚀 **Cloud Build Complete!**"
        echo "✅ APK built successfully in the cloud"
        echo "📥 Download APK from Actions artifacts"
        echo "🔥 Ready for Firebase Test Lab testing"
