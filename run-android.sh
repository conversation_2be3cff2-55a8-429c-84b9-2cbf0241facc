#!/bin/bash

echo "🤖 Iniciando Rebyu en Android"
echo "=============================="

# Verificar que estemos en el directorio correcto
if [ ! -f "frontend/capacitor.config.ts" ]; then
    echo "❌ Error: Ejecuta este script desde la raíz del proyecto"
    exit 1
fi

# Verificar que el backend esté corriendo
echo "🔍 Verificando backend..."
if ! curl -s http://localhost:9000/ > /dev/null; then
    echo "⚠️  Backend no está corriendo. Iniciándolo..."
    export PATH="/Users/<USER>/Library/Python/3.9/bin:$PATH"
    uvicorn main:app --host 0.0.0.0 --port 9000 --reload &
    BACKEND_PID=$!
    echo "⏳ Esperando que el backend esté listo..."
    sleep 5
else
    echo "✅ Backend está corriendo"
fi

cd frontend

echo "🏗️  Haciendo build del frontend..."
npm run build

echo "🔄 Sincronizando con Android..."
npx cap sync android

echo "📱 Abriendo en Android Studio..."
npx cap open android

echo ""
echo "🎯 Instrucciones para Android Studio:"
echo "======================================"
echo "1. Espera a que Gradle termine de sincronizar"
echo "2. Ve a Tools > AVD Manager"
echo "3. Crea un nuevo Virtual Device si no tienes uno"
echo "4. Inicia el emulador"
echo "5. Presiona el botón 'Run' (▶️) para instalar la app"
echo ""
echo "📝 Si hay problemas:"
echo "- Asegúrate de que el SDK esté instalado"
echo "- Verifica que las variables de entorno estén configuradas"
echo "- Reinicia Android Studio si es necesario"
echo ""
echo "🌐 El backend está en: http://localhost:9000"
echo "📱 La app se conectará automáticamente al backend"

if [ ! -z "$BACKEND_PID" ]; then
    echo ""
    echo "🛑 Presiona Ctrl+C para detener el backend"
    wait $BACKEND_PID
fi 