#!/bin/bash

# 🔄 Capacitor Live Reload - Testing sin APK
# ==========================================

echo "🔄 Configurando Capacitor Live Reload para Rebyu"
echo "================================================"

echo "📱 TESTING SIN APK - CAPACITOR LIVE RELOAD"
echo "=========================================="
echo ""
echo "Esta opción te permite probar tu app en dispositivos reales"
echo "sin necesidad de construir APK cada vez."
echo ""

echo "🔧 CONFIGURACIÓN:"
echo "================"
echo ""
echo "1️⃣  Obtener tu IP local:"
IP=$(ifconfig | grep "inet " | grep -v 127.0.0.1 | awk '{print $2}' | head -1)
echo "   Tu IP local: $IP"
echo ""

echo "2️⃣  Configurar Capacitor para desarrollo:"
cat > /Users/<USER>/Development/rebyiu/frontend/capacitor.config.dev.ts << EOF
import type { CapacitorConfig } from '@capacitor/cli';

const config: CapacitorConfig = {
  appId: 'io.rebyu.app',
  appName: 'Rebyu Dev',
  webDir: 'dist',
  bundledWebRuntime: false,
  server: {
    url: 'http://$IP:4321',
    cleartext: true
  },
  plugins: {
    StatusBar: {
      style: 'default',
      backgroundColor: '#000000'
    },
    SplashScreen: {
      launchShowDuration: 2000,
      backgroundColor: '#4A90E2',
      showSpinner: false
    }
  }
};

export default config;
EOF

echo "✅ Archivo capacitor.config.dev.ts creado"
echo ""

echo "3️⃣  COMANDOS PARA TESTING EN VIVO:"
echo "=================================="
echo ""
echo "# Terminal 1 - Iniciar servidor de desarrollo:"
echo "cd /Users/<USER>/Development/rebyiu/frontend"
echo "npm run dev"
echo ""
echo "# Terminal 2 - Sincronizar con configuración de desarrollo:"
echo "cd /Users/<USER>/Development/rebyiu/frontend"
echo "npx cap sync android --config capacitor.config.dev.ts"
echo ""
echo "# Terminal 3 - Abrir en Android Studio:"
echo "npx cap open android"
echo ""
echo "# En Android Studio:"
echo "# 1. Conecta tu dispositivo Android via USB"
echo "# 2. Habilita 'Depuración USB' en tu teléfono"
echo "# 3. Presiona 'Run' para instalar la app"
echo "# 4. La app se conectará a tu servidor local en tiempo real"
echo ""

echo "💡 VENTAJAS DEL LIVE RELOAD:"
echo "=========================="
echo "✅ Cambios instantáneos sin rebuild"
echo "✅ Debugging en tiempo real"
echo "✅ Testing en dispositivo real"
echo "✅ No necesitas resolver problemas de Gradle"
echo "✅ Conexión directa a tu backend localhost:9000"
echo ""

echo "📱 TESTING EN DISPOSITIVO FÍSICO:"
echo "================================"
echo "1. Conecta tu Android via USB"
echo "2. Habilita 'Opciones de desarrollador'"
echo "3. Activa 'Depuración USB'"
echo "4. Ejecuta los comandos de arriba"
echo "5. Tu app se actualizará automáticamente con cada cambio"

echo ""
echo "🌐 PARA TESTING REMOTO (sin USB):"
echo "================================="
echo "Usa ngrok para exponer tu servidor:"
echo "npm install -g ngrok"
echo "ngrok http 4321"
echo "# Usa la URL de ngrok en capacitor.config.dev.ts"
