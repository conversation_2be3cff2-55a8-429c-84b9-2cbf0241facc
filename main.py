from fastapi import Fast<PERSON><PERSON>, Depends, HTTPException
from sqlalchemy.orm import Session
from typing import List, Optional
import httpx
import os
import json
from fastapi.middleware.cors import CORSMiddleware

import database
import schemas

app = FastAPI()

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://localhost:5173", "capacitor://localhost", "http://localhost"],  # Frontend URLs
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Dependency
def get_db():
    db = database.SessionLocal()
    try:
        yield db
    finally:
        db.close()

@app.get("/")
async def root():
    return {"message": "Hello World"}


@app.get("/hello/{name}")
async def say_hello(name: str):
    return {"message": f"Hello {name}"}

# Create a new class
@app.post("/classes/", response_model=schemas.Class)
def create_class(class_item: schemas.ClassCreate, db: Session = Depends(get_db)):
    # Check if class with same name already exists
    db_class = db.query(database.ClassModel).filter(database.ClassModel.name == class_item.name).first()
    if db_class:
        raise HTTPException(status_code=400, detail="Class with this name already exists")

    # Create new class
    db_class = database.ClassModel(**class_item.dict())
    db.add(db_class)
    db.commit()
    db.refresh(db_class)
    return db_class

# Get a class by ID
@app.get("/classes/{class_id}", response_model=schemas.Class)
def get_class(class_id: int, db: Session = Depends(get_db)):
    db_class = db.query(database.ClassModel).filter(database.ClassModel.id == class_id).first()
    if db_class is None:
        raise HTTPException(status_code=404, detail="Class not found")
    return db_class

# Delete a class
@app.delete("/classes/{class_id}")
def delete_class(class_id: int, db: Session = Depends(get_db)):
    db_class = db.query(database.ClassModel).filter(database.ClassModel.id == class_id).first()
    if db_class is None:
        raise HTTPException(status_code=404, detail="Class not found")

    db.delete(db_class)
    db.commit()
    return {"message": f"Class with id {class_id} deleted successfully"}

# --- NUEVOS ENDPOINTS PARA ROADMAP ---

# Función para generar contenido con IA (OpenRoute o Gemini)
async def generate_content_with_ai(prompt: str):
    """
    Genera contenido utilizando OpenRoute API o Gemini como fallback
    """
    # Primero intentamos con OpenRoute
    try:
        # Configuración de la API key de OpenRoute
        OPENROUTE_API_KEY = os.getenv("OPENROUTE_API_KEY", "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")

        async with httpx.AsyncClient() as client:
            response = await client.post(
                "https://openroute.ai/api/v1/chat/completions",
                headers={
                    "Authorization": f"Bearer {OPENROUTE_API_KEY}",
                    "Content-Type": "application/json"
                },
                json={
                    "model": "openai/gpt-3.5-turbo",
                    "messages": [
                        {"role": "system", "content": "Eres un asistente educativo que crea contenido estructurado para roadmaps de aprendizaje."},
                        {"role": "user", "content": prompt}
                    ]
                },
                timeout=60.0
            )

            if response.status_code == 200:
                return response.json()["choices"][0]["message"]["content"]
    except Exception as e:
        print(f"Error con OpenRoute: {e}")

    # Si falla OpenRoute, intentamos con Gemini
    try:
        # Configuración de la API key de Gemini
        GEMINI_API_KEY = os.getenv("GEMINI_API_KEY", "AIzaSyBlmKj355297405ZtOYPpP4FCXrimQVltA")

        async with httpx.AsyncClient() as client:
            response = await client.post(
                "https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent",
                headers={
                    "Content-Type": "application/json"
                },
                params={
                    "key": GEMINI_API_KEY
                },
                json={
                    "contents": [
                        {
                            "parts": [
                                {"text": prompt}
                            ]
                        }
                    ]
                },
                timeout=60.0
            )

            if response.status_code == 200:
                return response.json()["candidates"][0]["content"]["parts"][0]["text"]
    except Exception as e:
        print(f"Error con Gemini: {e}")

    # Si ambos fallan, devolvemos un mensaje de error
    return "No se pudo generar contenido con IA. Por favor, intenta más tarde."

# Endpoint para crear un roadmap completo con IA
@app.post("/roadmaps/generate/", response_model=schemas.Roadmap)
async def generate_roadmap(request: schemas.RoadmapGenerateRequest, db: Session = Depends(get_db)):
    # Validar que se proporcionen exactamente 3 preguntas objetivo
    if len(request.objective_questions) != 3:
        raise HTTPException(status_code=400, detail="Se requieren exactamente 3 preguntas objetivo")

    # Crear el roadmap base
    roadmap = database.RoadmapModel(
        title=f"Roadmap sobre {request.topic}",
        description=request.description,
        topic=request.topic
    )
    db.add(roadmap)
    db.commit()
    db.refresh(roadmap)

    # Generar prompt para la IA según la nueva especificación
    prompt = f"""
    Crea un roadmap de aprendizaje completo sobre el tema: {request.topic}

    Basado en las siguientes preguntas objetivo:
    1. {request.objective_questions[0]}
    2. {request.objective_questions[1]}  
    3. {request.objective_questions[2]}

    Al crear un roadmap, estará compuesto por afirmaciones organizadas en 4 grados, y cada afirmación irá acompañada de 3 preguntas que la cuestionan, profundizan o ayudan a explicarla:

    - Grado 1: 3 afirmaciones principales, cada una corta y enfocada en un solo aspecto concreto del tema. Para cada afirmación, genera 3 preguntas abiertas que inviten a la reflexión y explicación oral.
    - Grado 2: Para cada afirmación de grado 1, genera 3 afirmaciones que desglosen ese aspecto en subaspectos clave, de forma menos general pero aún explicativa. Para cada afirmación de grado 2, genera 3 preguntas abiertas que la cuestionen o profundicen.
    - Grado 3: Para cada afirmación de grado 2, genera 3 afirmaciones muy específicas, concretas y medibles, cuya explicación sea corta y objetiva. Para cada afirmación de grado 3, genera 3 preguntas abiertas que la cuestionen o profundicen.
    - Grado 4: Para cada afirmación de grado 3, genera 3 afirmaciones aún más concretas, objetivas y puntuales. Para cada afirmación de grado 4, genera 3 preguntas abiertas que ayuden a explicar, justificar o ejemplificar la afirmación.

    Las afirmaciones principales (grado 1) deben ser breves, claras y centradas en un solo aspecto, para que todo el árbol de grados profundice únicamente en ese aspecto.
    Las preguntas deben estar redactadas para fomentar la explicación oral y la reflexión, evitando respuestas cerradas de sí/no.
    La estructura debe garantizar que el conocimiento de grado 4 es indispensable y suficiente para explicar y justificar los grados superiores.

    Devuelve el resultado en formato JSON con la siguiente estructura:
    {{
        "grado_1": [
            {{
                "afirmacion": "Afirmación de grado 1",
                "preguntas": [
                    "Pregunta 1 sobre esta afirmación",
                    "Pregunta 2 sobre esta afirmación", 
                    "Pregunta 3 sobre esta afirmación"
                ],
                "grado_2": [
                    {{
                        "afirmacion": "Afirmación de grado 2",
                        "preguntas": [
                            "Pregunta 1 sobre esta afirmación",
                            "Pregunta 2 sobre esta afirmación",
                            "Pregunta 3 sobre esta afirmación"
                        ],
                        "grado_3": [
                            {{
                                "afirmacion": "Afirmación de grado 3", 
                                "preguntas": [
                                    "Pregunta 1 sobre esta afirmación",
                                    "Pregunta 2 sobre esta afirmación",
                                    "Pregunta 3 sobre esta afirmación"
                                ],
                                "grado_4": [
                                    {{
                                        "afirmacion": "Afirmación de grado 4",
                                        "preguntas": [
                                            "Pregunta 1 sobre esta afirmación",
                                            "Pregunta 2 sobre esta afirmación", 
                                            "Pregunta 3 sobre esta afirmación"
                                        ]
                                    }}
                                ]
                            }}
                        ]
                    }}
                ]
            }}
        ]
    }}
    """

    # Por ahora, vamos a usar una estructura básica predefinida
    # Esto asegura que siempre tengamos contenido válido
    roadmap_data = {
        "grado_1": [
            {
                "afirmacion": f"Los fundamentos de {request.topic} son esenciales para el aprendizaje",
                "preguntas": [
                    request.objective_questions[0],
                    f"¿Por qué es importante dominar {request.topic}?",
                    f"¿Cuáles son los conceptos clave de {request.topic}?"
                ],
                "grado_2": [
                    {
                        "afirmacion": f"La sintaxis básica de {request.topic} define cómo escribir código",
                        "preguntas": [
                            request.objective_questions[1], 
                            f"¿Cuáles son las reglas sintácticas de {request.topic}?",
                            f"¿Qué herramientas ayudan con la sintaxis de {request.topic}?"
                        ],
                        "grado_3": [
                            {
                                "afirmacion": f"Los tipos de datos en {request.topic} determinan cómo se almacena la información",
                                "preguntas": [
                                    request.objective_questions[2],
                                    f"¿Cómo se declaran variables en {request.topic}?",
                                    f"¿Qué tipos de datos primitivos existen en {request.topic}?"
                                ],
                                "grado_4": [
                                    {
                                        "afirmacion": f"Las variables en {request.topic} se declaran con palabras clave específicas",
                                        "preguntas": [
                                            f"¿Qué palabras clave se usan para declarar variables en {request.topic}?",
                                            f"¿Cuál es la diferencia entre los diferentes tipos de declaración?",
                                            f"¿Qué buenas prácticas existen para nombrar variables?"
                                        ]
                                    }
                                ]
                            }
                        ]
                    }
                ]
            }
        ]
    }

    try:
        # Función recursiva para crear afirmaciones y preguntas
        def create_afirmaciones(afirmaciones_data, grade, roadmap_id=None, parent_id=None):
            for afirmacion_data in afirmaciones_data[:3]:  # Limitamos a 3 afirmaciones por nivel
                # Crear la afirmación
                afirmacion = database.AfirmacionModel(
                    content=afirmacion_data.get("afirmacion", ""),
                    grade=grade,
                    roadmap_id=roadmap_id,
                    parent_id=parent_id
                )
                db.add(afirmacion)
                db.flush()  # Para obtener el ID

                # Crear las preguntas asociadas a esta afirmación
                preguntas = afirmacion_data.get("preguntas", [])
                for pregunta_text in preguntas[:3]:  # Limitamos a 3 preguntas por afirmación
                    pregunta = database.PreguntaModel(
                        content=pregunta_text,
                        afirmacion_id=afirmacion.id
                    )
                    db.add(pregunta)

                # Procesar afirmaciones hijas si existen
                if grade < 4:  # Solo hasta grado 4
                    next_grade_key = f"grado_{grade + 1}"
                    if next_grade_key in afirmacion_data and afirmacion_data[next_grade_key]:
                        create_afirmaciones(afirmacion_data[next_grade_key], grade + 1, None, afirmacion.id)

        # Crear afirmaciones de grado 1, 2, 3 y 4
        grado_1_data = roadmap_data.get("grado_1", [])
        create_afirmaciones(grado_1_data, 1, roadmap.id)

        db.commit()
        db.refresh(roadmap)

        return roadmap

    except Exception as e:
        db.rollback()
        print(f"Error al generar roadmap: {e}")
        raise HTTPException(status_code=500, detail=f"Error al generar roadmap: {str(e)}")

# Endpoint para listar todos los roadmaps
@app.get("/roadmaps/", response_model=List[schemas.Roadmap])
def get_roadmaps(db: Session = Depends(get_db)):
    roadmaps = db.query(database.RoadmapModel).all()
    return roadmaps

# Endpoint para obtener un roadmap por ID
@app.get("/roadmaps/{roadmap_id}", response_model=schemas.Roadmap)
def get_roadmap(roadmap_id: int, db: Session = Depends(get_db)):
    roadmap = db.query(database.RoadmapModel).filter(database.RoadmapModel.id == roadmap_id).first()
    if roadmap is None:
        raise HTTPException(status_code=404, detail="Roadmap not found")

    return roadmap

# Endpoint para crear manualmente una pregunta
@app.post("/questions/", response_model=schemas.Question)
def create_question(question: schemas.QuestionCreate, db: Session = Depends(get_db)):
    # Validar que el grado sea válido (1, 2 o 3)
    if question.grade not in [1, 2, 3]:
        raise HTTPException(status_code=400, detail="Grade must be 1, 2 or 3")

    # Para preguntas de grado 1, necesitamos un roadmap_id
    if question.grade == 1 and question.roadmap_id is None:
        raise HTTPException(status_code=400, detail="Roadmap ID is required for grade 1 questions")

    # Para preguntas de grado 2 y 3, necesitamos un parent_id
    if question.grade > 1 and question.parent_id is None:
        raise HTTPException(status_code=400, detail=f"Parent ID is required for grade {question.grade} questions")

    # Verificar que el roadmap existe si se proporciona roadmap_id
    if question.roadmap_id:
        roadmap = db.query(database.RoadmapModel).filter(database.RoadmapModel.id == question.roadmap_id).first()
        if roadmap is None:
            raise HTTPException(status_code=404, detail="Roadmap not found")

    # Verificar que la pregunta padre existe y tiene el grado correcto
    if question.parent_id:
        parent = db.query(database.QuestionModel).filter(database.QuestionModel.id == question.parent_id).first()
        if parent is None:
            raise HTTPException(status_code=404, detail="Parent question not found")
        if parent.grade != question.grade - 1:
            raise HTTPException(status_code=400, detail=f"Parent question must be grade {question.grade - 1}")

    # Crear la pregunta
    db_question = database.QuestionModel(
        content=question.content,
        grade=question.grade,
        roadmap_id=question.roadmap_id if question.grade == 1 else None,
        parent_id=question.parent_id if question.grade > 1 else None
    )

    db.add(db_question)
    db.commit()
    db.refresh(db_question)
    return db_question
