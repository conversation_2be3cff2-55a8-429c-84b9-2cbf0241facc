from sqlalchemy import create_engine, Column, Integer, String, ForeignKey, Text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, relationship
import os

# For testing purposes, we'll use SQLite instead of PostgreSQL
# This will allow us to test the API without needing a valid Neon database connection
SQLALCHEMY_DATABASE_URL = "sqlite:///./classes.db"
engine = create_engine(SQLALCHEMY_DATABASE_URL, connect_args={"check_same_thread": False})

# Create SessionLocal class
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Create Base class
Base = declarative_base()

# Define Class model
class ClassModel(Base):
    __tablename__ = "classes"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, unique=True, index=True)
    description = Column(String)

# Nuevos modelos para el roadmap
class RoadmapModel(Base):
    __tablename__ = "roadmaps"

    id = Column(Integer, primary_key=True, index=True)
    title = Column(String, index=True)
    description = Column(String)
    topic = Column(String)

    # Relaciones
    afirmaciones = relationship("AfirmacionModel", back_populates="roadmap", cascade="all, delete-orphan")

class AfirmacionModel(Base):
    __tablename__ = "afirmaciones"

    id = Column(Integer, primary_key=True, index=True)
    content = Column(Text)  # El texto de la afirmación
    grade = Column(Integer)  # 1, 2, 3 o 4 para indicar el nivel
    roadmap_id = Column(Integer, ForeignKey("roadmaps.id"), nullable=True)
    parent_id = Column(Integer, ForeignKey("afirmaciones.id"), nullable=True)

    # Relaciones
    roadmap = relationship("RoadmapModel", back_populates="afirmaciones", foreign_keys=[roadmap_id])
    parent = relationship("AfirmacionModel", remote_side=[id], backref="children", foreign_keys=[parent_id])
    preguntas = relationship("PreguntaModel", back_populates="afirmacion", cascade="all, delete-orphan")

class PreguntaModel(Base):
    __tablename__ = "preguntas"

    id = Column(Integer, primary_key=True, index=True)
    content = Column(Text)  # El texto de la pregunta
    afirmacion_id = Column(Integer, ForeignKey("afirmaciones.id"))

    # Relaciones
    afirmacion = relationship("AfirmacionModel", back_populates="preguntas")

# Mantener el modelo anterior para compatibilidad
class QuestionModel(Base):
    __tablename__ = "questions"

    id = Column(Integer, primary_key=True, index=True)
    content = Column(Text)
    grade = Column(Integer)  # 1, 2 o 3 para indicar el nivel
    roadmap_id = Column(Integer, ForeignKey("roadmaps.id"), nullable=True)
    parent_id = Column(Integer, ForeignKey("questions.id"), nullable=True)

    # Relaciones
    roadmap = relationship("RoadmapModel", foreign_keys=[roadmap_id])
    parent = relationship("QuestionModel", remote_side=[id], backref="children", foreign_keys=[parent_id])

# Create tables
Base.metadata.create_all(bind=engine)

# Dependency to get DB session
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()
