{"name": "rebyu-frontend", "type": "module", "version": "0.0.1", "scripts": {"dev": "astro dev", "start": "astro dev", "build": "astro build", "preview": "astro preview", "astro": "astro", "cloud-build": "cd .. && ./cloud-build.sh", "dev-cloud": "cd .. && ./start-dev-environment.sh", "eas-build": "eas build --platform android --profile preview", "eas-dev": "eas build --platform android --profile development"}, "dependencies": {"@capacitor/android": "^5.0.0", "@capacitor/core": "^5.0.0", "@capacitor/device": "^5.0.8", "@capacitor/haptics": "^5.0.8", "@capacitor/ios": "^5.0.0", "@capacitor/keyboard": "^5.0.9", "@capacitor/network": "^5.0.8", "@capacitor/splash-screen": "^5.0.8", "@capacitor/status-bar": "^5.0.8", "astro": "^3.0.0"}, "devDependencies": {"@capacitor/cli": "^5.0.0", "typescript": "^5.8.3"}}