---
// MobileManager component para gestionar funcionalidades de Capacitor
---

<script>
import { StatusBar, Style } from '@capacitor/status-bar';
import { SplashScreen } from '@capacitor/splash-screen';
import { Keyboard } from '@capacitor/keyboard';
import { Device } from '@capacitor/device';
import { Network } from '@capacitor/network';
import { Haptics, ImpactStyle } from '@capacitor/haptics';

// Configuración inicial cuando la app carga
document.addEventListener('DOMContentLoaded', async () => {
  try {
    // Configurar StatusBar
    await StatusBar.setStyle({ style: Style.Light });
    await StatusBar.setBackgroundColor({ color: '#4a5568' });
    
    // Ocultar SplashScreen después de que todo esté listo
    await SplashScreen.hide();
    
    // Configurar teclado
    Keyboard.addListener('keyboardWillShow', () => {
      document.body.classList.add('keyboard-open');
    });
    
    Keyboard.addListener('keyboardWillHide', () => {
      document.body.classList.remove('keyboard-open');
    });
    
    // Información del dispositivo
    const device = await Device.getInfo();
    console.log('Device info:', device);
    
    // Estado de la red
    const status = await Network.getStatus();
    console.log('Network status:', status);
    
    // Listener para cambios de red
    Network.addListener('networkStatusChange', status => {
      console.log('Network status changed:', status);
      if (!status.connected) {
        showOfflineMessage();
      } else {
        hideOfflineMessage();
      }
    });
    
  } catch (error) {
    console.log('Mobile features not available:', error);
  }
});

// Función para agregar feedback háptico a botones
function addHapticFeedback() {
  const buttons = document.querySelectorAll('.btn, button, .card');
  buttons.forEach(button => {
    button.addEventListener('click', async () => {
      try {
        await Haptics.impact({ style: ImpactStyle.Light });
      } catch (error) {
        // Haptics no disponible en web
      }
    });
  });
}

// Mensajes de conectividad
function showOfflineMessage() {
  let offlineBar = document.getElementById('offline-bar');
  if (!offlineBar) {
    offlineBar = document.createElement('div');
    offlineBar.id = 'offline-bar';
    offlineBar.textContent = 'Sin conexión a internet';
    offlineBar.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      background: #e53e3e;
      color: white;
      text-align: center;
      padding: 8px;
      z-index: 1000;
      font-size: 14px;
    `;
    document.body.appendChild(offlineBar);
  }
}

function hideOfflineMessage() {
  const offlineBar = document.getElementById('offline-bar');
  if (offlineBar) {
    offlineBar.remove();
  }
}

// Agregar feedback háptico cuando el DOM esté listo
document.addEventListener('DOMContentLoaded', () => {
  setTimeout(addHapticFeedback, 100);
});
</script>

<style>
  /* Estilos para cuando el teclado está abierto */
  body.keyboard-open {
    transform: translateY(-50px);
    transition: transform 0.25s ease-out;
  }
  
  /* Mejoras para mobile */
  @media (max-width: 768px) {
    .container {
      padding: 1rem 0.5rem;
    }
    
    .btn {
      min-height: 44px; /* Tamaño mínimo recomendado para touch */
      font-size: 16px; /* Previene zoom en iOS */
    }
    
    input, textarea, select {
      font-size: 16px; /* Previene zoom en iOS */
      min-height: 44px;
    }
    
    /* Mejoras para cards en mobile */
    .card, .afirmacion-card {
      margin-bottom: 1rem;
      border-radius: 12px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }
  }
  
  /* Safe area para dispositivos con notch */
  @supports (padding: env(safe-area-inset-top)) {
    .safe-top {
      padding-top: env(safe-area-inset-top);
    }
    
    .safe-bottom {
      padding-bottom: env(safe-area-inset-bottom);
    }
  }
</style> 