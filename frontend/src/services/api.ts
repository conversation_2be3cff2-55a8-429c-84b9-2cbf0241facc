// Detectar si estamos en móvil (Capacitor) o en web
const isMobile = typeof window !== 'undefined' && 
  (window as any).Capacitor !== undefined;

// En emulador Android, localhost debe ser ********
const API_URL = isMobile 
  ? 'http://********:9000' 
  : 'http://localhost:9000';

export async function fetchRoadmaps() {
  console.log('🚀 Fetching roadmaps from:', `${API_URL}/roadmaps/`);
  console.log('🌐 Running in mobile?', isMobile);
  
  try {
    const response = await fetch(`${API_URL}/roadmaps/`);
    console.log('📡 Response status:', response.status);
    console.log('📡 Response headers:', response.headers);
    
    if (!response.ok) {
      throw new Error(`Failed to fetch roadmaps: ${response.status} ${response.statusText}`);
    }
    const data = await response.json();
    console.log('✅ Data received:', data);
    return data;
  } catch (error) {
    console.error('❌ Fetch error:', error);
    throw error;
  }
}

export async function fetchRoadmapById(id: number) {
  const response = await fetch(`${API_URL}/roadmaps/${id}`);
  if (!response.ok) {
    throw new Error('Failed to fetch roadmap');
  }
  return response.json();
}

export async function createRoadmap(data: {
  topic: string;
  description: string;
  objective_questions: string[];
}) {
  const response = await fetch(`${API_URL}/roadmaps/generate/`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  });

  if (!response.ok) {
    throw new Error('Failed to create roadmap');
  }

  return response.json();
}

// Add more API functions as needed for your application
