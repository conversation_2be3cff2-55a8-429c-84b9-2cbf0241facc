---
import Layout from '../layouts/Layout.astro';
---

<Layout title="Debug Mobile">
  <div class="debug-container">
    <h1>🔧 Debug Mobile</h1>
    
    <div class="test-section">
      <h2>JavaScript Básico</h2>
      <button id="js-test">Test JavaScript</button>
      <div id="js-result">Presiona el botón</div>
    </div>

    <div class="test-section">
      <h2>Capacitor Detection</h2>
      <div id="capacitor-info">Detectando...</div>
    </div>

    <div class="test-section">
      <h2>Event Listeners</h2>
      <button id="event-test">Test Event Listener</button>
      <div id="event-result">Esperando click...</div>
    </div>

    <div class="test-section">
      <h2>Fetch Test</h2>
      <button id="fetch-test">Test API Call</button>
      <div id="fetch-result">Listo para probar</div>
    </div>

    <div class="test-section">
      <h2>Console Logs</h2>
      <button id="log-test">Generate Console Logs</button>
    </div>
  </div>
</Layout>

<script>
  console.log('🚀 Debug page loaded');
  
  // Test JavaScript básico
  document.addEventListener('DOMContentLoaded', () => {
    console.log('✅ DOMContentLoaded fired');

    // JavaScript test
    const jsTestBtn = document.getElementById('js-test');
    const jsResult = document.getElementById('js-result');
    
    if (jsTestBtn && jsResult) {
      jsTestBtn.addEventListener('click', () => {
        console.log('🟢 JavaScript test button clicked');
        jsResult.textContent = `✅ JavaScript funciona! ${new Date().toLocaleTimeString()}`;
        jsResult.style.color = 'green';
      });
    }

    // Capacitor detection
    const capacitorInfo = document.getElementById('capacitor-info');
    if (capacitorInfo) {
      const isMobile = typeof window !== 'undefined' && 
        (window as any).Capacitor !== undefined;
      
      capacitorInfo.innerHTML = `
        <strong>Mobile:</strong> ${isMobile}<br>
        <strong>Capacitor:</strong> ${(window as any).Capacitor ? 'Detected' : 'Not found'}<br>
        <strong>User Agent:</strong> ${navigator.userAgent}<br>
        <strong>Platform:</strong> ${(window as any).Capacitor?.getPlatform?.() || 'web'}
      `;
      
      console.log('📱 Mobile detection:', isMobile);
      console.log('🔌 Capacitor:', (window as any).Capacitor);
    }

    // Event listener test
    const eventTestBtn = document.getElementById('event-test');
    const eventResult = document.getElementById('event-result');
    
    if (eventTestBtn && eventResult) {
      eventTestBtn.addEventListener('click', () => {
        console.log('🎯 Event listener test clicked');
        eventResult.textContent = `✅ Event listener funciona! ${Date.now()}`;
        eventResult.style.color = 'blue';
      });
    }

    // Fetch test
    const fetchTestBtn = document.getElementById('fetch-test');
    const fetchResult = document.getElementById('fetch-result');
    
    if (fetchTestBtn && fetchResult) {
      fetchTestBtn.addEventListener('click', async () => {
        console.log('🌐 Starting fetch test');
        fetchResult.textContent = 'Probando...';
        fetchResult.style.color = 'orange';
        
        try {
          const isMobile = (window as any).Capacitor !== undefined;
          const apiUrl = isMobile ? 'http://********:9000' : 'http://localhost:9000';
          
          console.log('📡 Fetching from:', `${apiUrl}/roadmaps/`);
          
          const response = await fetch(`${apiUrl}/roadmaps/`);
          console.log('📡 Response status:', response.status);
          
          if (response.ok) {
            const data = await response.json();
            fetchResult.textContent = `✅ API OK! Roadmaps: ${data.length}`;
            fetchResult.style.color = 'green';
            console.log('✅ Fetch successful:', data.length, 'roadmaps');
          } else {
            throw new Error(`HTTP ${response.status}`);
          }
        } catch (error) {
          console.error('❌ Fetch error:', error);
          fetchResult.textContent = `❌ Error: ${(error as Error).message}`;
          fetchResult.style.color = 'red';
        }
      });
    }

    // Console log test
    const logTestBtn = document.getElementById('log-test');
    if (logTestBtn) {
      logTestBtn.addEventListener('click', () => {
        console.log('📝 Regular log test');
        console.warn('⚠️ Warning test');
        console.error('❌ Error test');
        console.info('ℹ️ Info test');
        console.debug('🐛 Debug test');
        
        // Test multiple logs
        for (let i = 1; i <= 5; i++) {
          console.log(`🔢 Test log ${i}`);
        }
      });
    }

    console.log('🎉 All debug handlers attached');
  });
</script>

<style>
  .debug-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 1rem;
    font-family: monospace;
  }

  .test-section {
    margin: 2rem 0;
    padding: 1rem;
    border: 2px solid #ddd;
    border-radius: 8px;
    background: #f9f9f9;
  }

  .test-section h2 {
    margin-top: 0;
    color: #333;
  }

  button {
    background: #007bff;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
    margin: 8px 0;
    min-height: 44px; /* Touch-friendly */
    min-width: 120px;
  }

  button:hover {
    background: #0056b3;
  }

  button:active {
    background: #004085;
    transform: scale(0.98);
  }

  #js-result, #event-result, #fetch-result {
    margin-top: 1rem;
    padding: 1rem;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    min-height: 40px;
  }

  #capacitor-info {
    font-size: 14px;
    line-height: 1.6;
    background: #fff;
    padding: 1rem;
    border: 1px solid #ddd;
    border-radius: 4px;
  }

  /* Mobile optimizations */
  @media (max-width: 768px) {
    .debug-container {
      padding: 0.5rem;
    }
    
    button {
      width: 100%;
      margin: 8px 0;
    }
  }
</style> 