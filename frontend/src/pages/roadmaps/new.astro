---
import Layout from '../../layouts/Layout.astro';
---

<Layout title="Create Roadmap">
  <div class="container">
    <h1>Create New Roadmap</h1>

    <form id="roadmap-form">
      <div class="form-group">
        <label for="topic">Topic</label>
        <input type="text" id="topic" name="topic" required placeholder="e.g., JavaScript, Machine Learning, Photography">
      </div>

      <div class="form-group">
        <label for="description">Description</label>
        <textarea id="description" name="description" required placeholder="Describe what you want to learn and your current knowledge level..."></textarea>
      </div>

      <div class="objective-questions">
        <h3>Objective Questions</h3>
        <p class="help-text">Enter 3 key questions that you want to answer through this learning roadmap.</p>

        <div class="form-group">
          <label for="objective-question-1">Question 1</label>
          <input type="text" id="objective-question-1" name="objective-question-1" required placeholder="e.g., What are the fundamental concepts of JavaScript?">
        </div>

        <div class="form-group">
          <label for="objective-question-2">Question 2</label>
          <input type="text" id="objective-question-2" name="objective-question-2" required placeholder="e.g., How do I build interactive web applications with JavaScript?">
        </div>

        <div class="form-group">
          <label for="objective-question-3">Question 3</label>
          <input type="text" id="objective-question-3" name="objective-question-3" required placeholder="e.g., What are the best practices for JavaScript development?">
        </div>
      </div>

      <div class="form-actions">
        <button type="submit" class="btn primary">Generate Roadmap</button>
        <a href="/roadmaps" class="btn secondary">Cancel</a>
      </div>
    </form>

    <div id="status" class="status"></div>
  </div>
</Layout>

<script>
  // Client-side JavaScript
  document.addEventListener('DOMContentLoaded', () => {
    const form = document.getElementById('roadmap-form');
    const status = document.getElementById('status');

    form.addEventListener('submit', async (e) => {
      e.preventDefault();

      const formData = new FormData(form);
      const data = {
        topic: formData.get('topic'),
        description: formData.get('description'),
        objective_questions: [
          formData.get('objective-question-1'),
          formData.get('objective-question-2'),
          formData.get('objective-question-3')
        ]
      };

      status.textContent = 'Creating roadmap...';
      status.className = 'status loading';

      try {
        // Detectar si estamos en móvil para usar la IP correcta
        const isMobile = window.Capacitor !== undefined;
        const apiUrl = isMobile ? 'http://********:9000' : 'http://localhost:9000';
        
        const response = await fetch(`${apiUrl}/roadmaps/generate/`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(data),
        });

        if (!response.ok) {
          throw new Error('Failed to create roadmap');
        }

        const result = await response.json();
        status.textContent = 'Roadmap created successfully!';
        status.className = 'status success';

        // Redirect to the roadmap page after a short delay
        setTimeout(() => {
          window.location.href = `/roadmap?id=${result.id}`;
        }, 1500);
      } catch (error) {
        status.textContent = `Error: ${error.message}`;
        status.className = 'status error';
      }
    });
  });
</script>

<style>
  .container {
    max-width: 800px;
    margin: 0 auto;
    padding: 1rem;
  }

  h1 {
    margin-bottom: 2rem;
    text-align: center;
  }

  .form-group {
    margin-bottom: 1.5rem;
  }

  .objective-questions {
    background-color: #f7fafc;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    border: 1px solid #e2e8f0;
  }

  .objective-questions h3 {
    margin-top: 0;
    margin-bottom: 0.5rem;
    color: #2d3748;
  }

  .help-text {
    color: #718096;
    margin-bottom: 1.5rem;
    font-size: 0.9rem;
  }

  label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: bold;
    color: #4a5568;
  }

  input, textarea {
    width: 100%;
    padding: 0.8rem;
    border: 1px solid #e2e8f0;
    border-radius: 4px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
  }

  input:focus, textarea:focus {
    outline: none;
    border-color: #4a5568;
    box-shadow: 0 0 0 3px rgba(74, 85, 104, 0.2);
  }

  textarea {
    height: 150px;
    resize: vertical;
  }

  .form-actions {
    display: flex;
    justify-content: space-between;
    gap: 1rem;
  }

  .btn {
    display: inline-block;
    padding: 0.8rem 1.5rem;
    border-radius: 4px;
    font-weight: bold;
    cursor: pointer;
    transition: background-color 0.3s ease;
    text-align: center;
    flex: 1;
  }

  .primary {
    background-color: #4a5568;
    color: white;
    border: none;
  }

  .primary:hover {
    background-color: #2d3748;
  }

  .secondary {
    background-color: white;
    color: #4a5568;
    border: 1px solid #4a5568;
    text-decoration: none;
  }

  .secondary:hover {
    background-color: #f7fafc;
  }

  .status {
    margin-top: 2rem;
    padding: 1rem;
    border-radius: 4px;
    text-align: center;
    display: none;
  }

  .status.loading {
    display: block;
    background-color: #ebf8ff;
    color: #3182ce;
  }

  .status.success {
    display: block;
    background-color: #f0fff4;
    color: #38a169;
  }

  .status.error {
    display: block;
    background-color: #fff5f5;
    color: #e53e3e;
  }

  @media (max-width: 768px) {
    .form-actions {
      flex-direction: column;
    }
  }
</style>
