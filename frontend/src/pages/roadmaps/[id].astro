---
import Layout from '../../layouts/Layout.astro';

// Para build estático, necesitamos esta función pero no generaremos páginas específicas
// La página usará client-side routing para cargar dinámicamente
export function getStaticPaths() {
  return [];
}

const { id } = Astro.params;
---

<Layout title="Roadmap Details">
  <div class="container">
    <div id="loading" class="loading-container">
      <div class="loading-spinner"></div>
      <p>Cargando roadmap...</p>
    </div>

    <div id="error-container" class="error-container" style="display: none;">
      <h1>Error</h1>
      <p id="error-message" class="error"></p>
      <a href="/roadmaps" class="btn">Volver a Roadmaps</a>
    </div>

    <div id="roadmap-container" style="display: none;">
      <div class="roadmap-header">
        <h1 id="roadmap-title"></h1>
        <p id="roadmap-topic" class="topic"></p>
        <p id="roadmap-description" class="description"></p>
      </div>

      <div class="afirmaciones-container">
        <h2>Ruta de Aprendizaje</h2>
        <div id="afirmaciones-content" class="afirmaciones"></div>
      </div>

      <div class="actions">
        <a href="/roadmaps" class="btn secondary">Volver a Roadmaps</a>
      </div>
    </div>
  </div>
</Layout>

<script>
import { fetchRoadmapById } from '../../services/api';

// Obtener ID de la URL
const pathSegments = window.location.pathname.split('/');
const roadmapId = parseInt(pathSegments[pathSegments.length - 1]);

async function loadRoadmap() {
  const loadingEl = document.getElementById('loading');
  const errorEl = document.getElementById('error-container');
  const roadmapEl = document.getElementById('roadmap-container');
  const errorMessage = document.getElementById('error-message');

  try {
    const roadmap = await fetchRoadmapById(roadmapId);
    
    // Ocultar loading
    if (loadingEl) loadingEl.style.display = 'none';
    
    // Mostrar contenido del roadmap
    if (roadmapEl) roadmapEl.style.display = 'block';
    
    // Llenar contenido
    const titleEl = document.getElementById('roadmap-title');
    const topicEl = document.getElementById('roadmap-topic');
    const descriptionEl = document.getElementById('roadmap-description');
    const afirmacionesEl = document.getElementById('afirmaciones-content');
    
    if (titleEl) titleEl.textContent = roadmap.title;
    if (topicEl) topicEl.textContent = `Tema: ${roadmap.topic}`;
    if (descriptionEl) descriptionEl.textContent = roadmap.description;
    
    // Renderizar afirmaciones
    if (afirmacionesEl && roadmap.afirmaciones && roadmap.afirmaciones.length > 0) {
      afirmacionesEl.innerHTML = renderAfirmaciones(roadmap.afirmaciones);
    } else if (afirmacionesEl) {
      afirmacionesEl.innerHTML = '<p class="no-afirmaciones">No se encontraron afirmaciones para este roadmap.</p>';
    }
    
  } catch (error) {
    console.error('Error loading roadmap:', error);
    
    // Ocultar loading
    if (loadingEl) loadingEl.style.display = 'none';
    
    // Mostrar error
    if (errorEl) errorEl.style.display = 'block';
    if (errorMessage) errorMessage.textContent = error.message;
  }
}

function renderAfirmaciones(afirmaciones) {
  return afirmaciones.map((afirmacion, afirmacionIndex) => `
    <div class="afirmacion-card grade-1">
      <div class="afirmacion-header">
        <h3>
          <span class="afirmacion-number">G${afirmacion.grade}.${afirmacionIndex + 1}</span>
          ${afirmacion.content}
        </h3>
      </div>
      
      ${afirmacion.preguntas && afirmacion.preguntas.length > 0 ? `
        <div class="preguntas-section">
          <h4>Preguntas de reflexión:</h4>
          <ul class="preguntas-list">
            ${afirmacion.preguntas.map(pregunta => `
              <li class="pregunta-item">${pregunta.content}</li>
            `).join('')}
          </ul>
        </div>
      ` : ''}

      ${afirmacion.children && afirmacion.children.length > 0 ? `
        <div class="subafirmaciones">
          ${renderSubafirmaciones(afirmacion.children, 2)}
        </div>
      ` : ''}
    </div>
  `).join('');
}

function renderSubafirmaciones(subafirmaciones, grade) {
  return subafirmaciones.map((subafirmacion, index) => `
    <div class="afirmacion-card grade-${grade}">
      <div class="afirmacion-header">
        <h4>
          <span class="afirmacion-number">G${subafirmacion.grade}.${index + 1}</span>
          ${subafirmacion.content}
        </h4>
      </div>
      
      ${subafirmacion.preguntas && subafirmacion.preguntas.length > 0 ? `
        <div class="preguntas-section">
          <h5>Preguntas:</h5>
          <ul class="preguntas-list">
            ${subafirmacion.preguntas.map(pregunta => `
              <li class="pregunta-item">${pregunta.content}</li>
            `).join('')}
          </ul>
        </div>
      ` : ''}

      ${subafirmacion.children && subafirmacion.children.length > 0 ? `
        <div class="subafirmaciones">
          ${renderSubafirmaciones(subafirmacion.children, grade + 1)}
        </div>
      ` : ''}
    </div>
  `).join('');
}

// Cargar roadmap cuando el DOM esté listo
document.addEventListener('DOMContentLoaded', loadRoadmap);
</script>

<style>
  .container {
    padding: 1rem;
    max-width: 1000px;
    margin: 0 auto;
  }

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 4rem 2rem;
    text-align: center;
  }

  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #e2e8f0;
    border-top: 4px solid #4a5568;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  .roadmap-header {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e2e8f0;
  }

  h1 {
    margin-bottom: 0.5rem;
    color: #2d3748;
  }

  h2 {
    margin: 2rem 0 1rem;
    color: #2d3748;
  }

  h3 {
    margin: 1.5rem 0 0.5rem;
    color: #2d3748;
  }

  h4 {
    margin: 1rem 0 0.5rem;
    color: #4a5568;
  }

  h5 {
    margin: 1rem 0 0.5rem;
    color: #4a5568;
    font-size: 1rem;
  }

  .topic {
    color: #4a5568;
    font-weight: bold;
    margin-bottom: 0.5rem;
  }

  .description {
    color: #718096;
    margin-bottom: 1rem;
  }

  .afirmacion-card {
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    border-left: 4px solid;
  }

  .grade-1 {
    background-color: white;
    border-left-color: #3182ce;
  }

  .grade-2 {
    background-color: #f7fafc;
    border-radius: 6px;
    padding: 1rem;
    margin: 1rem 0;
    border-left-color: #38a169;
  }

  .grade-3 {
    background-color: #ebf8ff;
    margin-bottom: 0.5rem;
    padding: 1rem;
    border-radius: 4px;
    border-left-color: #ed8936;
  }

  .grade-4 {
    background-color: #fff5f5;
    margin-bottom: 0.5rem;
    padding: 1rem;
    border-radius: 4px;
    border-left-color: #e53e3e;
  }

  .afirmacion-number {
    display: inline-block;
    background-color: #4a5568;
    color: white;
    padding: 0.2rem 0.5rem;
    border-radius: 4px;
    margin-right: 0.5rem;
    font-size: 0.9rem;
    font-weight: bold;
  }

  .afirmacion-header {
    margin-bottom: 1rem;
  }

  .preguntas-section {
    background-color: rgba(74, 85, 104, 0.05);
    padding: 1rem;
    border-radius: 6px;
    margin-top: 1rem;
  }

  .preguntas-section h4, .preguntas-section h5, .preguntas-section h6 {
    margin: 0 0 0.5rem 0;
    color: #2d3748;
    font-size: 1rem;
  }

  .preguntas-list {
    margin: 0;
    padding-left: 1.5rem;
  }

  .pregunta-item {
    margin-bottom: 0.5rem;
    color: #4a5568;
    line-height: 1.5;
  }

  .afirmaciones {
    margin-top: 1rem;
  }

  .subafirmaciones {
    margin-top: 1rem;
    margin-left: 1.5rem;
  }

  .no-afirmaciones {
    text-align: center;
    padding: 2rem;
    background-color: #f7fafc;
    border-radius: 8px;
  }

  .actions {
    margin-top: 2rem;
    display: flex;
    justify-content: center;
  }

  .btn {
    display: inline-block;
    padding: 0.6rem 1.2rem;
    border-radius: 4px;
    text-decoration: none;
    font-weight: bold;
    transition: background-color 0.3s ease;
  }

  .secondary {
    background-color: white;
    color: #4a5568;
    border: 1px solid #4a5568;
  }

  .secondary:hover {
    background-color: #f7fafc;
  }

  .error-container {
    text-align: center;
    padding: 2rem;
    background-color: #fff5f5;
    border-radius: 8px;
    margin-bottom: 2rem;
  }

  .error {
    color: #e53e3e;
    margin-bottom: 1rem;
  }

  @media (max-width: 768px) {
    .afirmacion-card {
      padding: 1rem;
    }
  }
</style>
