---
import Layout from '../../layouts/Layout.astro';
// Para build estático, cargaremos los roadmaps usando client-side fetching
---

<Layout title="Roadmaps">
  <div class="container">
    <h1>Roadmaps de Aprendizaje</h1>
    
    <div id="loading" class="loading-container">
      <div class="loading-spinner"></div>
      <p>Cargando roadmaps...</p>
    </div>
    
    <div id="error-container" class="error" style="display: none;"></div>
    
    <div id="roadmaps-list" class="roadmaps-list" style="display: none;">
    </div>
    
    <div class="actions">
      <a href="/roadmaps/new" class="btn create">Crear Nuevo Roadmap</a>
    </div>
  </div>
</Layout>

<script>
import { fetchRoadmaps } from '../../services/api';

async function loadRoadmaps() {
  const loadingEl = document.getElementById('loading');
  const errorEl = document.getElementById('error-container');
  const roadmapsListEl = document.getElementById('roadmaps-list');

  try {
    const roadmaps = await fetchRoadmaps();
    
    // Ocultar loading
    if (loadingEl) loadingEl.style.display = 'none';
    
    // Mostrar lista de roadmaps
    if (roadmapsListEl) {
      roadmapsListEl.style.display = 'grid';
      
      if (roadmaps.length > 0) {
        roadmapsListEl.innerHTML = roadmaps.map((roadmap: any) => `
          <div class="roadmap-card">
            <h2>${roadmap.title}</h2>
            <p class="topic">Tema: ${roadmap.topic}</p>
            <p class="description">${roadmap.description}</p>
            <a href="/roadmap?id=${roadmap.id}" class="btn view">Ver Detalles</a>
          </div>
        `).join('');
      } else {
        roadmapsListEl.innerHTML = '<p class="no-roadmaps">No se encontraron roadmaps. ¡Crea uno!</p>';
      }
    }
    
  } catch (error) {
    console.error('Error loading roadmaps:', error);
    
    // Ocultar loading
    if (loadingEl) loadingEl.style.display = 'none';
    
    // Mostrar error
    if (errorEl) {
      errorEl.style.display = 'block';
      errorEl.textContent = `Error: ${(error as Error).message}`;
    }
  }
}

// Cargar roadmaps cuando el DOM esté listo
document.addEventListener('DOMContentLoaded', loadRoadmaps);
</script>

<style>
  .container {
    padding: 1rem;
  }

  h1 {
    margin-bottom: 2rem;
    text-align: center;
  }

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 4rem 2rem;
    text-align: center;
  }

  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #e2e8f0;
    border-top: 4px solid #4a5568;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  .roadmaps-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 2rem;
    margin: 2rem 0;
  }

  .roadmap-card {
    background-color: white;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
  }

  .roadmap-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  }

  .roadmap-card h2 {
    margin-bottom: 0.5rem;
    color: #2d3748;
  }

  .topic {
    color: #4a5568;
    font-weight: bold;
    margin-bottom: 0.5rem;
  }

  .description {
    color: #718096;
    margin-bottom: 1.5rem;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .btn {
    display: inline-block;
    padding: 0.6rem 1.2rem;
    border-radius: 4px;
    text-decoration: none;
    font-weight: bold;
    transition: background-color 0.3s ease;
  }

  .view {
    background-color: #4a5568;
    color: white;
  }

  .view:hover {
    background-color: #2d3748;
  }

  .create {
    background-color: #48bb78;
    color: white;
  }

  .create:hover {
    background-color: #38a169;
  }

  .actions {
    display: flex;
    justify-content: center;
    margin-top: 2rem;
  }

  .error {
    color: #e53e3e;
    text-align: center;
    margin-bottom: 1rem;
    padding: 0.5rem;
    background-color: #fff5f5;
    border-radius: 4px;
  }

  .no-roadmaps {
    text-align: center;
    grid-column: 1 / -1;
    padding: 2rem;
    background-color: #f7fafc;
    border-radius: 8px;
  }

  @media (max-width: 768px) {
    .roadmaps-list {
      grid-template-columns: 1fr;
    }
  }
</style>