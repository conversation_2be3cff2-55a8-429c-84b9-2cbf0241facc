---
import Layout from '../layouts/Layout.astro';
---

<Layout title="Rebyu Mobile App">
  <div id="app-container">
    <!-- Navigation Tabs -->
    <div class="tab-bar">
      <button class="tab-btn active" data-page="home">🏠 Home</button>
      <button class="tab-btn" data-page="roadmaps">📚 Roadmaps</button>
      <button class="tab-btn" data-page="new-roadmap">✨ Create</button>
      <button class="tab-btn" data-page="debug">🔧 Debug</button>
    </div>

    <!-- Content Container -->
    <div id="content-container">
      <!-- Home Page -->
      <div id="page-home" class="page active">
        <div class="hero">
          <h1>Welcome to Rebyu</h1>
          <p>Your personalized learning roadmap generator</p>
          <div class="cta-buttons">
            <button class="btn primary" onclick="navigateToPage('roadmaps')">View Roadmaps</button>
            <button class="btn secondary" onclick="navigateToPage('new-roadmap')">Create New Roadmap</button>
            <button class="btn debug" onclick="navigateToPage('debug')">🔧 Debug Mobile</button>
          </div>
        </div>

        <section class="features">
          <h2>Features</h2>
          <div class="feature-grid">
            <div class="feature-card">
              <h3>AI-Generated Roadmaps</h3>
              <p>Create personalized learning paths with our AI-powered roadmap generator.</p>
            </div>
            <div class="feature-card">
              <h3>Structured Learning</h3>
              <p>Follow a structured approach with units, subunits, and questions.</p>
            </div>
            <div class="feature-card">
              <h3>Mobile Friendly</h3>
              <p>Access your roadmaps on any device with our responsive design.</p>
            </div>
          </div>
        </section>
      </div>

      <!-- Roadmaps Page -->
      <div id="page-roadmaps" class="page">
        <div class="container">
          <h1>My Learning Roadmaps</h1>
          <div id="roadmaps-list">
            <p>Loading roadmaps...</p>
          </div>
        </div>
      </div>

      <!-- New Roadmap Page -->
      <div id="page-new-roadmap" class="page">
        <div class="container">
          <h1>Create New Roadmap</h1>
          <form id="roadmap-form">
            <div class="form-group">
              <label for="topic">What do you want to learn?</label>
              <input type="text" id="topic" name="topic" placeholder="e.g., Python Programming, Digital Marketing" required>
            </div>
            <div class="form-group">
              <label for="difficulty">Difficulty Level</label>
              <select id="difficulty" name="difficulty">
                <option value="beginner">Beginner</option>
                <option value="intermediate">Intermediate</option>
                <option value="advanced">Advanced</option>
              </select>
            </div>
            <button type="submit" class="btn primary">Generate Roadmap</button>
          </form>
        </div>
      </div>

      <!-- Debug Page -->
      <div id="page-debug" class="page">
        <div class="debug-container">
          <h1>🔧 Debug Mobile</h1>
          
          <div class="test-section">
            <h2>Navigation Test</h2>
            <button class="btn" onclick="navigateToPage('home')">Go to Home</button>
            <button class="btn" onclick="navigateToPage('roadmaps')">Go to Roadmaps</button>
            <div id="nav-result">Navigation working!</div>
          </div>

          <div class="test-section">
            <h2>JavaScript Test</h2>
            <button id="js-test-btn" class="btn">Test JavaScript</button>
            <div id="js-result">Presiona el botón</div>
          </div>

          <div class="test-section">
            <h2>API Test</h2>
            <button id="api-test-btn" class="btn">Test API Call</button>
            <div id="api-result">Ready for API test</div>
          </div>

          <div class="test-section">
            <h2>Capacitor Info</h2>
            <div id="capacitor-info">Loading device info...</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</Layout>

<script>
  console.log('🚀 Single Page App loaded');

  // Global navigation function
  function navigateToPage(pageId) {
    console.log('🧭 Navigating to:', pageId);
    
    // Hide all pages
    const pages = document.querySelectorAll('.page');
    pages.forEach(page => page.classList.remove('active'));
    
    // Show target page
    const targetPage = document.getElementById(`page-${pageId}`);
    if (targetPage) {
      targetPage.classList.add('active');
      console.log('✅ Page displayed:', pageId);
    }
    
    // Update tab bar
    const tabs = document.querySelectorAll('.tab-btn');
    tabs.forEach(tab => tab.classList.remove('active'));
    
    const activeTab = document.querySelector(`[data-page="${pageId}"]`);
    if (activeTab) {
      activeTab.classList.add('active');
    }
    
    // Run page-specific initialization
    initializePage(pageId);
  }

  function initializePage(pageId) {
    switch(pageId) {
      case 'roadmaps':
        loadRoadmaps();
        break;
      case 'debug':
        initializeDebugPage();
        break;
    }
  }

  async function loadRoadmaps() {
    const container = document.getElementById('roadmaps-list');
    try {
      console.log('📡 Loading roadmaps...');
      
      // Detectar si estamos en móvil para usar la IP correcta
      const isMobile = window.Capacitor !== undefined;
      const apiUrl = isMobile ? 'http://********:9000' : 'http://localhost:9000';
      
      const response = await fetch(`${apiUrl}/roadmaps/`);
      if (!response.ok) throw new Error(`HTTP ${response.status}`);
      
      const roadmaps = await response.json();
      console.log('✅ Roadmaps loaded:', roadmaps);
      
      if (roadmaps.length === 0) {
        container.innerHTML = '<p>No roadmaps yet. Create your first one!</p>';
      } else {
        container.innerHTML = roadmaps.map(roadmap => `
          <div class="roadmap-card">
            <h3>${roadmap.topic}</h3>
            <p>Difficulty: ${roadmap.difficulty_level}</p>
            <button class="btn secondary" onclick="viewRoadmap(${roadmap.id})">View Details</button>
          </div>
        `).join('');
      }
    } catch (error) {
      console.error('❌ Error loading roadmaps:', error);
      container.innerHTML = '<p style="color: red;">Error loading roadmaps. Check API connection.</p>';
    }
  }

  function viewRoadmap(id) {
    console.log('👁️ Viewing roadmap:', id);
    alert(`Viewing roadmap ${id} - Feature coming soon!`);
  }

  function initializeDebugPage() {
    console.log('🔧 Initializing debug page');
    
    // Capacitor info
    const capacitorInfo = document.getElementById('capacitor-info');
    const info = {
      'Capacitor': window.Capacitor ? '✅ Detected' : '❌ Not detected',
      'Platform': window.Capacitor?.platform || 'web',
      'User Agent': navigator.userAgent,
      'Location': window.location.href
    };
    
    capacitorInfo.innerHTML = Object.entries(info)
      .map(([key, value]) => `<div><strong>${key}:</strong> ${value}</div>`)
      .join('');
  }

  // Make functions global
  window.navigateToPage = navigateToPage;
  window.viewRoadmap = viewRoadmap;

  // Tab bar navigation
  document.addEventListener('click', (e) => {
    if (e.target.classList.contains('tab-btn')) {
      const pageId = e.target.getAttribute('data-page');
      navigateToPage(pageId);
    }
  });

  // Debug button handlers
  document.addEventListener('DOMContentLoaded', () => {
    // JS Test button
    const jsTestBtn = document.getElementById('js-test-btn');
    if (jsTestBtn) {
      jsTestBtn.addEventListener('click', () => {
        const result = document.getElementById('js-result');
        result.textContent = `✅ JavaScript working! ${new Date().toLocaleTimeString()}`;
        result.style.color = 'green';
      });
    }

    // API Test button
    const apiTestBtn = document.getElementById('api-test-btn');
    if (apiTestBtn) {
      apiTestBtn.addEventListener('click', async () => {
        const result = document.getElementById('api-result');
        result.textContent = 'Testing API...';
        
        try {
          const isMobile = window.Capacitor !== undefined;
          const apiUrl = isMobile ? 'http://********:9000' : 'http://localhost:9000';
          
          const response = await fetch(`${apiUrl}/roadmaps/`);
          result.textContent = `✅ API working! Status: ${response.status}`;
          result.style.color = 'green';
        } catch (error) {
          result.textContent = `❌ API error: ${error.message}`;
          result.style.color = 'red';
        }
      });
    }

    // Form submission
    const form = document.getElementById('roadmap-form');
    if (form) {
      form.addEventListener('submit', async (e) => {
        e.preventDefault();
        
        const formData = new FormData(form);
        const data = {
          topic: formData.get('topic'),
          difficulty: formData.get('difficulty')
        };
        
        console.log('📝 Creating roadmap:', data);
        alert('Roadmap creation - Feature coming soon!');
      });
    }

    console.log('✅ Event listeners attached');
  });
</script>

<style>
  .tab-bar {
    display: flex;
    background: #4a5568;
    padding: 0;
    position: sticky;
    top: 0;
    z-index: 100;
  }

  .tab-btn {
    flex: 1;
    padding: 1rem;
    border: none;
    background: transparent;
    color: white;
    cursor: pointer;
    font-size: 0.9rem;
    transition: background-color 0.3s;
  }

  .tab-btn:hover, .tab-btn.active {
    background: #2d3748;
  }

  #content-container {
    position: relative;
    min-height: calc(100vh - 60px);
  }

  .page {
    display: none;
    padding: 1rem;
    min-height: 100%;
  }

  .page.active {
    display: block;
  }

  .hero {
    text-align: center;
    padding: 3rem 1rem;
    background-color: #f0f4f8;
    border-radius: 8px;
    margin-bottom: 2rem;
  }

  .hero h1 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
  }

  .hero p {
    font-size: 1.2rem;
    color: #4a5568;
    margin-bottom: 2rem;
  }

  .cta-buttons {
    display: flex;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
  }

  .btn {
    display: inline-block;
    padding: 0.8rem 1.5rem;
    border-radius: 4px;
    border: none;
    font-weight: bold;
    transition: background-color 0.3s ease;
    cursor: pointer;
    min-height: 44px;
  }

  .primary {
    background-color: #4a5568;
    color: white;
  }

  .primary:hover {
    background-color: #2d3748;
  }

  .secondary {
    background-color: white;
    color: #4a5568;
    border: 1px solid #4a5568;
  }

  .secondary:hover {
    background-color: #f7fafc;
  }

  .debug {
    background-color: #e53e3e;
    color: white;
    font-size: 0.9rem;
  }

  .features {
    margin-top: 3rem;
  }

  .features h2 {
    text-align: center;
    margin-bottom: 2rem;
  }

  .feature-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
  }

  .feature-card {
    background-color: white;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .feature-card h3 {
    margin-bottom: 1rem;
    color: #2d3748;
  }

  .container {
    max-width: 800px;
    margin: 0 auto;
    padding: 1rem;
  }

  .form-group {
    margin-bottom: 1rem;
  }

  .form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: bold;
  }

  .form-group input,
  .form-group select {
    width: 100%;
    padding: 0.8rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 16px;
    min-height: 44px;
  }

  .debug-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 1rem;
  }

  .test-section {
    margin: 2rem 0;
    padding: 1rem;
    border: 2px solid #ddd;
    border-radius: 8px;
    background: #f9f9f9;
  }

  .test-section h2 {
    margin-top: 0;
    color: #333;
  }

  #js-result, #api-result, #nav-result, #capacitor-info {
    margin-top: 1rem;
    padding: 1rem;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    min-height: 40px;
  }

  .roadmap-card {
    background: white;
    padding: 1rem;
    margin: 1rem 0;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  @media (max-width: 768px) {
    .hero h1 {
      font-size: 2rem;
    }

    .feature-grid {
      grid-template-columns: 1fr;
    }

    .tab-btn {
      font-size: 0.8rem;
      padding: 0.8rem 0.5rem;
    }

    .btn {
      width: 100%;
      margin: 0.5rem 0;
    }
  }
</style>