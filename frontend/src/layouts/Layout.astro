---
import MobileManager from '../components/MobileManager.astro';

export interface Props {
  title: string;
}

const { title } = Astro.props;
---

<!DOCTYPE html>
<html lang="es">
  <head>
    <meta charset="UTF-8" />
    <meta name="description" content="Rebyu - Tu generador de roadmaps de aprendizaje personalizado" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <meta name="generator" content={Astro.generator} />
    
    <!-- PWA Meta Tags -->
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />
    <meta name="theme-color" content="#4a5568" />
    <link rel="manifest" href="/manifest.json" />
    
    <title>{title} | Rebyu</title>
  </head>
  <body>
    <div class="app-container">
      <div class="main-content">
        <slot />
      </div>
      
      <!-- Mobile Manager Component -->
      <MobileManager />
      
      <footer class="footer">
        <p>&copy; 2024 Rebyu. Hecho con ❤️ y Astro.</p>
      </footer>
    </div>
  </body>
</html>

<style>
  :root {
    --primary-color: #4a5568;
    --secondary-color: #2d3748;
    --text-color: #1a202c;
    --background-color: #f7fafc;
  }

  * {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
  }

  body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen,
      Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    background-color: var(--background-color);
    color: var(--text-color);
    line-height: 1.6;
  }

  header {
    background-color: var(--primary-color);
    color: white;
    padding: 1rem;
  }

  nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
  }

  .logo a {
    font-size: 1.5rem;
    font-weight: bold;
    color: white;
    text-decoration: none;
  }

  nav ul {
    display: flex;
    list-style: none;
  }

  nav ul li {
    margin-left: 1rem;
  }

  nav ul li a {
    color: white;
    text-decoration: none;
  }

  main {
    max-width: 1200px;
    margin: 2rem auto;
    padding: 0 1rem;
  }

  footer {
    background-color: var(--secondary-color);
    color: white;
    text-align: center;
    padding: 1rem;
    margin-top: 2rem;
  }
</style>