import type { CapacitorConfig } from '@capacitor/cli';

// Get local IP for development
const getLocalIP = () => {
  // This will be replaced by the setup script with actual IP
  return '************';
};

const config: CapacitorConfig = {
  appId: 'io.rebyu.app.dev',
  appName: 'Rebyu Dev',
  webDir: 'dist',
  bundledWebRuntime: false,
  server: {
    url: `http://${getLocalIP()}:4321`,
    cleartext: true,
    allowNavigation: [
      `http://${getLocalIP()}:4321`,
      'REPLACE_WITH_TUNNEL_URL',  // Will be replaced with ngrok/cloudflare URL
      'http://localhost:9000',
      'https://*.ngrok.io',
      'https://*.trycloudflare.com'
    ]
  },
  plugins: {
    StatusBar: {
      style: 'default',
      backgroundColor: '#000000'
    },
    SplashScreen: {
      launchShowDuration: 1000,
      backgroundColor: '#4A90E2',
      showSpinner: true
    },
    Keyboard: {
      resize: 'body',
      style: 'dark'
    },
    // Enable live reload for development
    CapacitorHttp: {
      enabled: true
    }
  }
};

export default config;
