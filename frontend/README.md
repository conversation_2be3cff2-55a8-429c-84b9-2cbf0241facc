# Rebyu Frontend

A mobile-friendly frontend for the Rebyu application, built with Astro and Capacitor.

## Features

- View and create learning roadmaps
- Mobile-friendly design
- Integration with Rebyu API
- Native mobile app capabilities with Capacitor

## Development

### Prerequisites

- Node.js 16+
- npm or yarn

### Setup

1. Install dependencies:

```bash
npm install
```

2. Start the development server:

```bash
npm run dev
```

The application will be available at http://localhost:5173.

## Building for Production

### Web

To build for web deployment:

```bash
npm run build
```

This will generate static files in the `dist` directory.

### Mobile (Android/iOS)

1. Build the web application:

```bash
npm run build
```

2. Sync the web build with Capacitor:

```bash
npx cap sync
```

3. Open the project in Android Studio or Xcode:

```bash
# For Android
npx cap open android

# For iOS
npx cap open ios
```

4. Build and run the app from Android Studio or Xcode.

## Docker

The frontend can be run in a Docker container:

```bash
# Build the Docker image
docker build -t rebyu-frontend .

# Run the container
docker run -p 5173:5173 rebyu-frontend
```

Or using Docker Compose from the project root:

```bash
docker-compose up
```

## Project Structure

- `src/pages/`: Astro pages
- `src/layouts/`: Layout components
- `src/services/`: API services
- `public/`: Static assets

## Configuration

- `astro.config.mjs`: Astro configuration
- `capacitor.config.ts`: Capacitor configuration
