import type { CapacitorConfig } from '@capacitor/cli';

const config: CapacitorConfig = {
  appId: 'io.rebyu.app',
  appName: 'Rebyu',
  webDir: 'dist',
  bundledWebRuntime: false,
  server: {
    androidScheme: 'https',
    iosScheme: 'rebyu',
    cleartext: true,
    allowNavigation: [
      'http://********:9000',
      'http://localhost:9000'
    ]
  },
  plugins: {
    StatusBar: {
      style: 'default',
      backgroundColor: '#000000'
    },
    SplashScreen: {
      launchShowDuration: 2000,
      backgroundColor: '#4A90E2',
      showSpinner: false
    },
    Keyboard: {
      resize: 'body',
      style: 'dark'
    }
  }
};

export default config;