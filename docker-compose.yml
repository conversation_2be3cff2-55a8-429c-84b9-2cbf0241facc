version: '3'

services:
  rebyu-api:
    build: .
    container_name: rebyu-api
    ports:
      - "9000:8000"
    volumes:
      - .:/app
    environment:
      - DATABASE_URL=postgresql://default:<EMAIL>/classes_db?sslmode=require
      - OPENROUTE_API_KEY=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
      - GEMINI_API_KEY=AIzaSyBlmKj355297405ZtOYPpP4FCXrimQVltA
    restart: unless-stopped

  frontend:
    build: ./frontend
    container_name: rebyu-frontend
    ports:
      - "5173:5173"
    volumes:
      - ./frontend:/app
    depends_on:
      - rebyu-api
    restart: unless-stopped
