#!/bin/bash

# 🔥 Start Rebyu Development Environment
# =====================================

echo "🔥 Starting Rebyu Cloud Development Environment"
echo "=============================================="

# Function to cleanup on exit
cleanup() {
    echo "🛑 Shutting down development environment..."
    kill $(jobs -p) 2>/dev/null
    exit 0
}

trap cleanup SIGINT SIGTERM

# Start backend server
echo "🐍 Starting backend server..."
cd /Users/<USER>/Development/rebyiu
uvicorn main:app --host 0.0.0.0 --port 9000 --reload &
BACKEND_PID=$!

# Wait for backend to start
sleep 3

# Start ngrok tunnel for backend
echo "🌐 Starting ngrok tunnel for backend..."
ngrok http 9000 --log=stdout > ngrok.log 2>&1 &
NGROK_PID=$!

# Wait for ngrok to start and get URL
sleep 5
TUNNEL_URL=$(curl -s http://localhost:4040/api/tunnels | jq -r '.tunnels[0].public_url' 2>/dev/null || echo "")

if [ -n "$TUNNEL_URL" ]; then
    echo "✅ Backend tunnel: $TUNNEL_URL"
    
    # Update Capacitor config with tunnel URL
    sed -i.bak "s|REPLACE_WITH_TUNNEL_URL|$TUNNEL_URL|g" frontend/capacitor.config.dev.ts
else
    echo "⚠️  Could not get tunnel URL, check ngrok.log"
fi

# Start frontend development server
echo "🌐 Starting frontend development server..."
cd frontend
npm run dev &
FRONTEND_PID=$!

echo ""
echo "🚀 Development environment ready!"
echo "================================"
echo "📱 Frontend: http://localhost:4321"
echo "🐍 Backend: http://localhost:9000"
echo "🌐 Public Backend: $TUNNEL_URL"
echo ""
echo "📝 Next steps:"
echo "1. Make code changes in your editor"
echo "2. Changes will auto-reload in browser"
echo "3. Use 'npm run cloud-build' to build APK in cloud"
echo "4. Use 'npm run test-cloud' to test on Firebase Test Lab"
echo ""
echo "Press Ctrl+C to stop all services"

# Wait for all background processes
wait
