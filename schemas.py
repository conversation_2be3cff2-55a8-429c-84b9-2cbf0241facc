from pydantic import BaseModel
from typing import List, Optional

class ClassBase(BaseModel):
    name: str
    description: str

class ClassCreate(ClassBase):
    pass

class Class(ClassBase):
    id: int

    class Config:
        orm_mode = True

# Nuevos modelos para el roadmap con afirmaciones
class PreguntaBase(BaseModel):
    content: str

class PreguntaCreate(PreguntaBase):
    afirmacion_id: int

class Pregunta(PreguntaBase):
    id: int
    afirmacion_id: int

    class Config:
        orm_mode = True

class AfirmacionBase(BaseModel):
    content: str
    grade: int  # 1, 2, 3 o 4 para indicar el nivel

class AfirmacionCreate(AfirmacionBase):
    parent_id: Optional[int] = None
    roadmap_id: Optional[int] = None

class Afirmacion(AfirmacionBase):
    id: int
    roadmap_id: Optional[int] = None
    parent_id: Optional[int] = None
    preguntas: List[Pregunta] = []
    children: List['Afirmacion'] = []

    class Config:
        orm_mode = True

# Mantener modelos anteriores para compatibilidad
class QuestionBase(BaseModel):
    content: str
    grade: int  # 1, 2 o 3 para indicar el nivel

class QuestionCreate(QuestionBase):
    parent_id: Optional[int] = None  # Para preguntas de grado 2 y 3
    roadmap_id: Optional[int] = None  # Para preguntas de grado 1

class Question(QuestionBase):
    id: int
    roadmap_id: Optional[int] = None  # Para preguntas de grado 1
    parent_id: Optional[int] = None  # Para preguntas de grado 2 y 3
    children: List['Question'] = []

    class Config:
        orm_mode = True

class RoadmapBase(BaseModel):
    title: str
    description: str
    topic: str  # Tema para generar contenido con IA

class RoadmapCreate(RoadmapBase):
    pass

class Roadmap(RoadmapBase):
    id: int
    afirmaciones: List[Afirmacion] = []

    class Config:
        orm_mode = True

# Esquema para solicitar generación de roadmap con IA
class RoadmapGenerateRequest(BaseModel):
    topic: str
    description: str
    objective_questions: List[str]  # 3 preguntas objetivo

# Actualizar forward references
Afirmacion.update_forward_refs()
Question.update_forward_refs()
