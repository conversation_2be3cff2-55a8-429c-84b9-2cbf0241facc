#!/bin/bash

# 🌐 ngrok Tunnel para Backend Rebyu
# ==================================

echo "🌐 Configurando ngrok Tunnel para Backend Rebyu"
echo "==============================================="

echo "✅ ngrok ya está instalado"
echo ""

echo "🔧 CONFIGURACIÓN RÁPIDA:"
echo "======================="
echo ""
echo "1️⃣  Registrarse en ngrok (gratis):"
echo "   https://dashboard.ngrok.com/signup"
echo ""
echo "2️⃣  Obtener tu authtoken:"
echo "   https://dashboard.ngrok.com/get-started/your-authtoken"
echo ""
echo "3️⃣  Configurar authtoken:"
echo "   ngrok config add-authtoken TU_TOKEN_AQUI"
echo ""

echo "🚀 COMANDOS PARA USAR NGROK CON REBYU:"
echo "====================================="
echo ""
echo "# Terminal 1 - Iniciar backend Rebyu:"
echo "cd /Users/<USER>/Development/rebyiu"
echo "uvicorn main:app --host 0.0.0.0 --port 9000 --reload"
echo ""
echo "# Terminal 2 - Iniciar tunnel ngrok:"
echo "ngrok http 9000"
echo ""
echo "# ngrok te dará una URL como:"
echo "# https://abc123.ngrok.io -> http://localhost:9000"
echo ""

echo "📱 ACTUALIZAR CONFIGURACIÓN DE CAPACITOR:"
echo "========================================"

# Crear script para actualizar configuración automáticamente
cat > /Users/<USER>/Development/rebyiu/update-capacitor-config.sh << 'EOF'
#!/bin/bash

# Script para actualizar Capacitor config con URL de ngrok
echo "🔧 Actualizando configuración de Capacitor..."

if [ -z "$1" ]; then
    echo "❌ Error: Proporciona la URL de ngrok"
    echo "Uso: $0 https://abc123.ngrok.io"
    exit 1
fi

NGROK_URL="$1"

# Backup del archivo original
cp frontend/capacitor.config.ts frontend/capacitor.config.ts.backup

# Crear nueva configuración
cat > frontend/capacitor.config.ts << EOL
import type { CapacitorConfig } from '@capacitor/cli';

const config: CapacitorConfig = {
  appId: 'io.rebyu.app',
  appName: 'Rebyu',
  webDir: 'dist',
  bundledWebRuntime: false,
  server: {
    androidScheme: 'https',
    iosScheme: 'rebyu',
    cleartext: true,
    allowNavigation: [
      '${NGROK_URL}',                     // ngrok tunnel
      'http://localhost:9000'             // desarrollo local
    ]
  },
  plugins: {
    StatusBar: {
      style: 'default',
      backgroundColor: '#000000'
    },
    SplashScreen: {
      launchShowDuration: 2000,
      backgroundColor: '#4A90E2',
      showSpinner: false
    },
    Keyboard: {
      resize: 'body',
      style: 'dark'
    }
  }
};

export default config;
EOL

echo "✅ Configuración actualizada con URL: $NGROK_URL"
echo "📁 Backup guardado en: frontend/capacitor.config.ts.backup"
EOF

chmod +x /Users/<USER>/Development/rebyiu/update-capacitor-config.sh

echo "✅ Script de actualización creado: update-capacitor-config.sh"
echo ""

echo "📋 FLUJO COMPLETO PARA TESTING:"
echo "=============================="
echo ""
echo "1️⃣  Iniciar backend:"
echo "   cd /Users/<USER>/Development/rebyiu"
echo "   uvicorn main:app --host 0.0.0.0 --port 9000 --reload &"
echo ""
echo "2️⃣  Iniciar ngrok tunnel:"
echo "   ngrok http 9000"
echo "   # Copia la URL HTTPS que aparece"
echo ""
echo "3️⃣  Actualizar configuración Capacitor:"
echo "   ./update-capacitor-config.sh https://abc123.ngrok.io"
echo ""
echo "4️⃣  Construir y sincronizar:"
echo "   cd frontend"
echo "   npm run build"
echo "   npx cap sync android"
echo ""
echo "5️⃣  Construir APK (si Gradle funciona) o usar alternativas:"
echo "   cd android"
echo "   ./gradlew assembleDebug"
echo ""
echo "6️⃣  Probar en Firebase Test Lab:"
echo "   /Users/<USER>/google-cloud-sdk/bin/gcloud firebase test android run \\"
echo "     --type robo \\"
echo "     --app app/build/outputs/apk/debug/app-debug.apk \\"
echo "     --device model=MediumPhone.arm,version=30 \\"
echo "     --project mobile-testing-1748195029"
echo ""

echo "💡 VENTAJAS DE NGROK:"
echo "==================="
echo "✅ Setup súper rápido (5 minutos)"
echo "✅ Gratis para uso básico"
echo "✅ HTTPS automático"
echo "✅ URLs estables durante la sesión"
echo "✅ Dashboard web para monitoreo"
echo "✅ Compatible con Firebase Test Lab"
echo ""

echo "⚠️  LIMITACIONES TIER GRATUITO:"
echo "=============================="
echo "• URL cambia cada vez que reinicias ngrok"
echo "• Límite de conexiones concurrentes"
echo "• Sesiones de 2 horas máximo"
echo ""

echo "🔄 ALTERNATIVA PARA DESARROLLO CONTINUO:"
echo "======================================="
echo "Para evitar cambiar la URL constantemente:"
echo "1. Suscríbete a ngrok Pro ($8/mes)"
echo "2. Usa dominios personalizados estáticos"
echo "3. O usa Cloudflare Tunnel (gratis, más estable)"

echo ""
echo "🎯 PRÓXIMO PASO:"
echo "==============="
echo "1. Regístrate en https://dashboard.ngrok.com/signup"
echo "2. Copia tu authtoken"
echo "3. Ejecuta: ngrok config add-authtoken TU_TOKEN"
echo "4. Sigue el flujo completo de arriba"
