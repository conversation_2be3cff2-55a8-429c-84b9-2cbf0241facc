import sqlite3
import json
import subprocess
import time

# Connect to SQLite database
conn = sqlite3.connect('classes.db')
conn.row_factory = sqlite3.Row
cursor = conn.cursor()

# Function to execute curl commands
def execute_curl(command):
    result = subprocess.run(command, shell=True, capture_output=True, text=True)
    print(f"Command: {command}")
    print(f"Status code: {result.returncode}")
    print(f"Output: {result.stdout}")
    if result.stderr:
        print(f"Error: {result.stderr}")
    return result

# Function to migrate roadmaps
def migrate_roadmaps():
    print("Migrating roadmaps...")
    cursor.execute("SELECT * FROM roadmaps")
    roadmaps = cursor.fetchall()

    for roadmap in roadmaps:
        # For each roadmap, we need to create a request with 3 objective questions
        # Since the old schema doesn't have objective questions, we'll create generic ones
        objective_questions = [
            f"What are the fundamentals of {roadmap['topic']}?",
            f"How to apply {roadmap['topic']} in real-world scenarios?",
            f"What are the advanced concepts in {roadmap['topic']}?"
        ]

        data = {
            "topic": roadmap['topic'],
            "description": roadmap['description'],
            "objective_questions": objective_questions
        }

        # Create the roadmap using the API
        # Escape single quotes in the JSON data
        json_data = json.dumps(data).replace("'", "\\'")
        command = f"""curl -X POST "http://localhost:8000/roadmaps/generate/" \
            -H "Content-Type: application/json" \
            -d '{json_data}'"""

        result = execute_curl(command)

        # Wait for the background task to complete
        print("Waiting for background task to complete...")
        time.sleep(5)

        print(f"Roadmap {roadmap['id']} migrated.")

# Function to migrate questions
def migrate_questions():
    print("Migrating questions...")

    # First, get all roadmaps to map old IDs to new IDs
    command = """curl -X GET "http://localhost:8000/roadmaps/" \
        -H "Content-Type: application/json" """

    result = execute_curl(command)

    try:
        roadmaps = json.loads(result.stdout)
        # Create a mapping from old roadmap IDs to new roadmap IDs
        # We'll use the title and topic to match roadmaps
        roadmap_id_map = {}

        # Get the old roadmaps from SQLite
        cursor.execute("SELECT * FROM roadmaps")
        old_roadmaps = cursor.fetchall()

        # For each old roadmap, find the corresponding new roadmap
        for old_roadmap in old_roadmaps:
            old_id = old_roadmap['id']
            old_title = old_roadmap['title']
            old_topic = old_roadmap['topic']

            # Find the new roadmap with the same title and topic
            for new_roadmap in roadmaps:
                if new_roadmap['title'] == old_title and new_roadmap['topic'] == old_topic:
                    roadmap_id_map[old_id] = new_roadmap['id']
                    break

        print(f"Roadmap ID mapping: {roadmap_id_map}")

        # Now, for each question in the old database, create a new question
        cursor.execute("""
            SELECT q.id, q.content, s.unit_id, u.roadmap_id 
            FROM questions q 
            JOIN subunits s ON q.subunit_id = s.id 
            JOIN units u ON s.unit_id = u.id
        """)
        questions = cursor.fetchall()

        for question in questions:
            old_roadmap_id = question['roadmap_id']
            new_roadmap_id = roadmap_id_map.get(old_roadmap_id)

            if new_roadmap_id:
                # Create a grade 1 question
                data = {
                    "content": question['content'],
                    "grade": 1,
                    "roadmap_id": new_roadmap_id,
                    "parent_id": None
                }

                # Escape single quotes in the JSON data
                json_data = json.dumps(data).replace("'", "\\'")
                command = f"""curl -X POST "http://localhost:8000/questions/" \
                    -H "Content-Type: application/json" \
                    -d '{json_data}'"""

                result = execute_curl(command)

                try:
                    new_question = json.loads(result.stdout)
                    question_id = new_question['id']

                    # Now get subquestions for this question
                    cursor.execute("""
                        SELECT * FROM subquestions 
                        WHERE question_id = ? AND grade = 1
                    """, (question['id'],))
                    subquestions_g1 = cursor.fetchall()

                    for subq_g1 in subquestions_g1:
                        # Create a grade 2 question
                        data = {
                            "content": subq_g1['content'],
                            "grade": 2,
                            "roadmap_id": None,
                            "parent_id": question_id
                        }

                        # Escape single quotes in the JSON data
                        json_data = json.dumps(data).replace("'", "\\'")
                        command = f"""curl -X POST "http://localhost:8000/questions/" \
                            -H "Content-Type: application/json" \
                            -d '{json_data}'"""

                        result = execute_curl(command)

                        try:
                            new_subq_g1 = json.loads(result.stdout)
                            subq_g1_id = new_subq_g1['id']

                            # Get grade 2 subquestions
                            cursor.execute("""
                                SELECT * FROM subquestions 
                                WHERE parent_id = ? AND grade = 2
                            """, (subq_g1['id'],))
                            subquestions_g2 = cursor.fetchall()

                            for subq_g2 in subquestions_g2:
                                # Create a grade 3 question
                                data = {
                                    "content": subq_g2['content'],
                                    "grade": 3,
                                    "roadmap_id": None,
                                    "parent_id": subq_g1_id
                                }

                                # Escape single quotes in the JSON data
                                json_data = json.dumps(data).replace("'", "\\'")
                                command = f"""curl -X POST "http://localhost:8000/questions/" \
                                    -H "Content-Type: application/json" \
                                    -d '{json_data}'"""

                                execute_curl(command)

                        except json.JSONDecodeError:
                            print(f"Failed to parse response for subquestion grade 1: {subq_g1['id']}")

                except json.JSONDecodeError:
                    print(f"Failed to parse response for question: {question['id']}")
            else:
                print(f"Could not find new roadmap ID for old roadmap ID: {old_roadmap_id}")

    except json.JSONDecodeError:
        print("Failed to parse roadmaps response")

# Function to migrate classes
def migrate_classes():
    print("Migrating classes...")
    cursor.execute("SELECT * FROM classes")
    classes = cursor.fetchall()

    for class_item in classes:
        data = {
            "name": class_item['name'],
            "description": class_item['description']
        }

        # Escape single quotes in the JSON data
        json_data = json.dumps(data).replace("'", "\\'")
        command = f"""curl -X POST "http://localhost:8000/classes/" \
            -H "Content-Type: application/json" \
            -d '{json_data}'"""

        execute_curl(command)
        print(f"Class {class_item['id']} migrated.")

# Main migration function
def migrate_data():
    print("Starting data migration from SQLite to Neon DB...")

    # Migrate classes
    migrate_classes()

    # Migrate roadmaps
    migrate_roadmaps()

    # Migrate questions
    migrate_questions()

    print("Data migration completed.")

if __name__ == "__main__":
    migrate_data()
