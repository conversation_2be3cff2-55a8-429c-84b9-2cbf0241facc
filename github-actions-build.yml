# 🚀 GitHub Actions para construir APK de Rebyu
# =============================================
# Archivo: .github/workflows/build-android.yml

name: Build Android APK

on:
  push:
    branches: [ main ]
  workflow_dispatch:

jobs:
  build:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v3
      
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        
    - name: Setup Java
      uses: actions/setup-java@v3
      with:
        distribution: 'temurin'
        java-version: '11'
        
    - name: Setup Android SDK
      uses: android-actions/setup-android@v2
      
    - name: Install dependencies
      run: |
        cd frontend
        npm install
        
    - name: Build web app
      run: |
        cd frontend
        npm run build
        
    - name: Sync Capacitor
      run: |
        cd frontend
        npx cap sync android
        
    - name: Build Android APK
      run: |
        cd frontend/android
        chmod +x gradlew
        ./gradlew assembleDebug
        
    - name: Upload APK
      uses: actions/upload-artifact@v3
      with:
        name: rebyu-debug-apk
        path: frontend/android/app/build/outputs/apk/debug/app-debug.apk
