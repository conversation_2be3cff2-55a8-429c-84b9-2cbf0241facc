#!/bin/bash

# 📊 Monitor GitHub Actions Build & Firebase Test Lab
# ===================================================

echo "📊 Monitoreando Build de Rebyu en la Nube"
echo "=========================================="

# Colors
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m'

# GitHub repository info
REPO_URL="https://github.com/Tobarrientos2/rebyiu"
ACTIONS_URL="$REPO_URL/actions"

echo -e "${BLUE}🌐 GitHub Actions:${NC} $ACTIONS_URL"
echo -e "${BLUE}🔥 Firebase Test Lab:${NC} https://console.firebase.google.com/project/mobile-testing-1748195029/testlab"
echo ""

# Function to check build status
check_build_status() {
    echo -e "${YELLOW}📋 Estado del Build:${NC}"
    echo "==================="
    echo "1. Ve a: $ACTIONS_URL"
    echo "2. Busca el build más reciente"
    echo "3. Estados posibles:"
    echo "   🟡 En progreso - Esperando..."
    echo "   ✅ Completado - APK listo para descargar"
    echo "   ❌ Fallido - Revisar logs"
    echo ""
}

# Function to show what to do when build completes
show_next_steps() {
    echo -e "${BLUE}📥 Cuando el Build Complete:${NC}"
    echo "=========================="
    echo "1. Ir a: $ACTIONS_URL"
    echo "2. Hacer clic en el build completado"
    echo "3. Scroll down hasta 'Artifacts'"
    echo "4. Descargar 'rebyu-debug-apk-[hash]'"
    echo "5. Extraer el ZIP para obtener el APK"
    echo "6. Ejecutar: ./firebase-cloud-test.sh test"
    echo ""
}

# Function to prepare Firebase Test Lab
prepare_firebase_test() {
    echo -e "${BLUE}🔥 Preparando Firebase Test Lab:${NC}"
    echo "==============================="
    
    # Verify Firebase Test Lab is ready
    echo "✅ Verificando dispositivos disponibles..."
    /Users/<USER>/google-cloud-sdk/bin/gcloud firebase test android models list | head -5
    
    echo ""
    echo -e "${GREEN}✅ Firebase Test Lab listo para testing${NC}"
    echo ""
    
    echo -e "${YELLOW}📱 Dispositivos recomendados para Rebyu:${NC}"
    echo "• MediumPhone.arm (Android 30) - Teléfono mediano"
    echo "• Pixel2.arm (Android 28) - Google Pixel 2"
    echo "• AndroidTablet270dpi.arm (Android 30) - Tablet"
    echo ""
}

# Function to show testing commands
show_test_commands() {
    echo -e "${BLUE}🚀 Comandos para Testing:${NC}"
    echo "======================="
    echo ""
    echo "# Cuando tengas el APK descargado:"
    echo "mv ~/Downloads/rebyu-debug-apk-*.zip ."
    echo "unzip rebyu-debug-apk-*.zip"
    echo "mv app-debug.apk rebyu-app.apk"
    echo ""
    echo "# Ejecutar test en Firebase Test Lab:"
    echo "./firebase-cloud-test.sh test"
    echo ""
    echo "# O comando manual:"
    echo "/Users/<USER>/google-cloud-sdk/bin/gcloud firebase test android run \\"
    echo "  --type robo \\"
    echo "  --app rebyu-app.apk \\"
    echo "  --device model=MediumPhone.arm,version=30,locale=en,orientation=portrait \\"
    echo "  --timeout 5m \\"
    echo "  --project mobile-testing-1748195029"
    echo ""
}

# Function to open relevant pages
open_monitoring_pages() {
    echo -e "${BLUE}🌐 Abriendo páginas de monitoreo...${NC}"
    
    # Open GitHub Actions
    open "$ACTIONS_URL" 2>/dev/null || echo "GitHub Actions: $ACTIONS_URL"
    
    # Open Firebase Test Lab
    open "https://console.firebase.google.com/project/mobile-testing-1748195029/testlab" 2>/dev/null || echo "Firebase Test Lab: https://console.firebase.google.com/project/mobile-testing-1748195029/testlab"
}

# Function to show build timeline
show_build_timeline() {
    echo -e "${YELLOW}⏱️  Timeline Esperado del Build:${NC}"
    echo "=============================="
    echo "🚀 Build iniciado (hace ~5 minutos)"
    echo "├── 📦 Setup Environment (2-3 min)"
    echo "├── 🏗️ Build Web App (1-2 min)"
    echo "├── 🔄 Sync Capacitor (30 seg)"
    echo "├── 🔧 Fix Gradle (30 seg)"
    echo "├── 📱 Build APK (3-5 min)"
    echo "└── 📤 Upload Artifact (30 seg)"
    echo ""
    echo "⏱️ Total: 7-11 minutos"
    echo "🕐 Debería completarse en los próximos 5-10 minutos"
    echo ""
}

# Main execution
main() {
    case "$1" in
        "status")
            check_build_status
            ;;
        "prepare")
            prepare_firebase_test
            ;;
        "commands")
            show_test_commands
            ;;
        "open")
            open_monitoring_pages
            ;;
        "timeline")
            show_build_timeline
            ;;
        "")
            echo -e "${GREEN}🎯 Monitoreo Completo de Rebyu${NC}"
            echo "============================="
            echo ""
            show_build_timeline
            check_build_status
            prepare_firebase_test
            show_test_commands
            echo -e "${BLUE}💡 Comandos disponibles:${NC}"
            echo "• $0 status    - Ver estado del build"
            echo "• $0 prepare   - Verificar Firebase Test Lab"
            echo "• $0 commands  - Ver comandos de testing"
            echo "• $0 open      - Abrir páginas de monitoreo"
            echo "• $0 timeline  - Ver timeline del build"
            ;;
        *)
            echo "Uso: $0 [status|prepare|commands|open|timeline]"
            ;;
    esac
}

main "$@"
