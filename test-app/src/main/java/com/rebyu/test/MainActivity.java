package com.rebyu.test;

import android.app.Activity;
import android.os.Bundle;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Button;
import android.view.View;
import android.widget.Toast;

public class MainActivity extends Activity {
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        LinearLayout layout = new LinearLayout(this);
        layout.setOrientation(LinearLayout.VERTICAL);
        layout.setPadding(50, 50, 50, 50);
        
        TextView title = new TextView(this);
        title.setText("🔥 Rebyu Test App");
        title.setTextSize(24);
        title.setPadding(0, 0, 0, 30);
        
        TextView subtitle = new TextView(this);
        subtitle.setText("Testing Firebase Test Lab");
        subtitle.setTextSize(16);
        subtitle.setPadding(0, 0, 0, 30);
        
        Button button1 = new Button(this);
        button1.setText("Test Button 1");
        button1.setOnClickListener(new View.OnClickListener() {
            public void onClick(View v) {
                Toast.makeText(MainActivity.this, "Button 1 clicked!", Toast.LENGTH_SHORT).show();
            }
        });
        
        Button button2 = new Button(this);
        button2.setText("Test Button 2");
        button2.setOnClickListener(new View.OnClickListener() {
            public void onClick(View v) {
                Toast.makeText(MainActivity.this, "Button 2 clicked!", Toast.LENGTH_SHORT).show();
            }
        });
        
        layout.addView(title);
        layout.addView(subtitle);
        layout.addView(button1);
        layout.addView(button2);
        
        setContentView(layout);
    }
}
