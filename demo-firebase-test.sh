#!/bin/bash

# 🔥 Firebase Test Lab Demo - Working Example
# ===========================================

GCLOUD_PATH="/Users/<USER>/google-cloud-sdk/bin/gcloud"
PROJECT_ID="mobile-testing-1748195029"

echo "🔥 Firebase Test Lab - Demo Funcional"
echo "====================================="

# Download a working APK for demonstration
download_demo_apk() {
    echo "📱 Descargando APK de demostración..."
    
    # Try multiple sources for a working APK
    SOURCES=(
        "https://storage.googleapis.com/test-lab-wd9m8b4g550sy-kyahe8b8wd/NotesList.apk"
        "https://github.com/firebase/quickstart-testing/raw/master/android/app/app-debug.apk"
        "https://raw.githubusercontent.com/firebase/quickstart-testing/master/android/app/app-debug.apk"
    )
    
    for source in "${SOURCES[@]}"; do
        echo "🔄 Intentando descargar desde: $source"
        if curl -L -o demo-app.apk "$source" && [ -s demo-app.apk ]; then
            # Check if it's actually an APK (not HTML)
            if file demo-app.apk | grep -q "Android"; then
                echo "✅ APK descargado exitosamente"
                return 0
            else
                echo "⚠️  Archivo descargado no es un APK válido"
                rm -f demo-app.apk
            fi
        else
            echo "❌ Fallo al descargar desde esta fuente"
            rm -f demo-app.apk
        fi
    done
    
    echo "❌ No se pudo descargar un APK válido"
    return 1
}

# Create a minimal APK using Android SDK (if available)
create_minimal_apk() {
    echo "🛠️  Creando APK mínimo..."
    
    # Check if we have Android SDK
    if [ ! -d "/Users/<USER>/Library/Android/sdk" ]; then
        echo "❌ Android SDK no encontrado"
        return 1
    fi
    
    # Create minimal app structure
    mkdir -p minimal-app
    cd minimal-app
    
    # Create a very basic APK using aapt (if available)
    ANDROID_HOME="/Users/<USER>/Library/Android/sdk"
    
    # Find available build tools
    BUILD_TOOLS_DIR=$(find "$ANDROID_HOME/build-tools" -maxdepth 1 -type d | tail -1)
    
    if [ -z "$BUILD_TOOLS_DIR" ]; then
        echo "❌ Build tools no encontrados"
        cd ..
        return 1
    fi
    
    echo "📋 Usando build tools: $BUILD_TOOLS_DIR"
    
    # For now, let's skip the complex APK creation and focus on testing
    cd ..
    echo "⚠️  Creación de APK compleja - saltando por ahora"
    return 1
}

# Test with a sample command (without actual APK)
demo_firebase_command() {
    echo ""
    echo "🔥 Demostración de Firebase Test Lab"
    echo "===================================="
    
    echo "📱 Comando que usarías para probar tu APK:"
    echo ""
    echo "$GCLOUD_PATH firebase test android run \\"
    echo "  --type robo \\"
    echo "  --app tu-app.apk \\"
    echo "  --device model=MediumPhone.arm,version=30,locale=en,orientation=portrait \\"
    echo "  --timeout 5m \\"
    echo "  --project $PROJECT_ID"
    echo ""
    
    echo "📋 Dispositivos disponibles:"
    echo "============================"
    $GCLOUD_PATH firebase test android models list | head -10
    
    echo ""
    echo "🎯 Para probar tu app Rebyu:"
    echo "============================"
    echo "1. Construye tu APK:"
    echo "   cd frontend/android"
    echo "   ./gradlew assembleDebug"
    echo ""
    echo "2. Ejecuta el test:"
    echo "   $GCLOUD_PATH firebase test android run \\"
    echo "     --type robo \\"
    echo "     --app app/build/outputs/apk/debug/app-debug.apk \\"
    echo "     --device model=MediumPhone.arm,version=30"
    echo ""
    echo "3. Ve los resultados en:"
    echo "   https://console.firebase.google.com/project/$PROJECT_ID/testlab"
}

# Test with actual APK if available
test_actual_apk() {
    if [ -f "demo-app.apk" ]; then
        echo ""
        echo "🚀 Ejecutando test real con APK"
        echo "==============================="
        
        echo "📱 Información del test:"
        echo "  APK: demo-app.apk"
        echo "  Tamaño: $(ls -lh demo-app.apk | awk '{print $5}')"
        echo "  Proyecto: $PROJECT_ID"
        echo ""
        
        echo "🔥 Iniciando test en Firebase Test Lab..."
        
        $GCLOUD_PATH firebase test android run \
            --type robo \
            --app demo-app.apk \
            --device model=MediumPhone.arm,version=30,locale=en,orientation=portrait \
            --timeout 3m \
            --project "$PROJECT_ID"
        
        echo ""
        echo "✅ Test completado!"
        echo "📊 Ver resultados en: https://console.firebase.google.com/project/$PROJECT_ID/testlab"
    else
        echo "⚠️  No hay APK disponible para test real"
    fi
}

# Show comprehensive guide
show_guide() {
    echo ""
    echo "📚 GUÍA COMPLETA - Firebase Test Lab"
    echo "===================================="
    echo ""
    echo "🔧 CONFIGURACIÓN (YA COMPLETADA):"
    echo "✅ Google Cloud CLI instalado"
    echo "✅ Proyecto creado: $PROJECT_ID"
    echo "✅ Firebase Test Lab API habilitada"
    echo "✅ Autenticación configurada"
    echo ""
    echo "📱 PARA TU APP REBYU:"
    echo "===================="
    echo ""
    echo "1️⃣  Arreglar problemas de Gradle:"
    echo "   cd frontend/android"
    echo "   # Editar variables.gradle para usar compileSdk compatible"
    echo "   # Actualizar build.gradle con versión de Gradle compatible"
    echo ""
    echo "2️⃣  Construir APK:"
    echo "   ./gradlew clean"
    echo "   ./gradlew assembleDebug"
    echo ""
    echo "3️⃣  Probar en Firebase Test Lab:"
    echo "   $GCLOUD_PATH firebase test android run \\"
    echo "     --type robo \\"
    echo "     --app app/build/outputs/apk/debug/app-debug.apk \\"
    echo "     --device model=MediumPhone.arm,version=30"
    echo ""
    echo "🎯 ALTERNATIVAS SI GRADLE FALLA:"
    echo "==============================="
    echo "• Usar Android Studio para build manual"
    echo "• Crear APK con herramientas online"
    echo "• Usar React Native CLI si es React Native"
    echo "• Usar Flutter build si es Flutter"
    echo ""
    echo "💡 BENEFICIOS DE FIREBASE TEST LAB:"
    echo "=================================="
    echo "✅ Sin necesidad de emuladores locales"
    echo "✅ Pruebas en dispositivos reales"
    echo "✅ Screenshots automáticos"
    echo "✅ Videos de sesión"
    echo "✅ Logs detallados"
    echo "✅ 10 tests gratuitos por día"
    echo ""
    echo "🌐 ACCESO A RESULTADOS:"
    echo "======================"
    echo "https://console.firebase.google.com/project/$PROJECT_ID/testlab"
}

# Main execution
main() {
    case "$1" in
        "download")
            download_demo_apk
            ;;
        "test")
            test_actual_apk
            ;;
        "demo")
            demo_firebase_command
            ;;
        "guide")
            show_guide
            ;;
        "full"|"")
            echo "🚀 Ejecutando demostración completa..."
            
            # Try to download a demo APK
            if download_demo_apk; then
                test_actual_apk
            else
                echo "⚠️  No se pudo descargar APK, mostrando demo de comandos"
                demo_firebase_command
            fi
            
            show_guide
            ;;
        *)
            echo "Uso: $0 [comando]"
            echo ""
            echo "Comandos:"
            echo "  download - Descargar APK de demo"
            echo "  test     - Probar APK (si existe)"
            echo "  demo     - Mostrar comandos de ejemplo"
            echo "  guide    - Mostrar guía completa"
            echo "  full     - Demo completa (default)"
            ;;
    esac
}

main "$@"
