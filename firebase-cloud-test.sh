#!/bin/bash

# 🔥 Firebase Test Lab Cloud Integration
# ======================================

set -e

echo "🔥 Firebase Test Lab Cloud Integration for Rebyu"
echo "==============================================="

GCLOUD_PATH="/Users/<USER>/google-cloud-sdk/bin/gcloud"
PROJECT_ID="mobile-testing-1748195029"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Function to test with EAS-built APK
test_eas_apk() {
    echo -e "${BLUE}📱 Testing EAS-built APK on Firebase Test Lab...${NC}"
    
    # Check if APK exists from EAS build
    if [ -f "rebyu-eas.apk" ]; then
        APK_FILE="rebyu-eas.apk"
    elif [ -f "frontend/android/app/build/outputs/apk/debug/app-debug.apk" ]; then
        APK_FILE="frontend/android/app/build/outputs/apk/debug/app-debug.apk"
    else
        echo -e "${RED}❌ No APK found. Build one first with:${NC}"
        echo "   npm run eas-build"
        echo "   or"
        echo "   npm run cloud-build"
        exit 1
    fi
    
    echo -e "${YELLOW}📱 Testing APK: $APK_FILE${NC}"
    
    # Run Firebase Test Lab
    $GCLOUD_PATH firebase test android run \
        --type robo \
        --app "$APK_FILE" \
        --device model=MediumPhone.arm,version=30,locale=en,orientation=portrait \
        --device model=Pixel2.arm,version=28,locale=en,orientation=portrait \
        --timeout 5m \
        --project "$PROJECT_ID" \
        --results-bucket="rebyu-cloud-test-results" \
        --results-dir="test-$(date +%Y%m%d-%H%M%S)"
    
    echo -e "${GREEN}✅ Test completed!${NC}"
    echo -e "${BLUE}🌐 View results: https://console.firebase.google.com/project/$PROJECT_ID/testlab${NC}"
}

# Function to download APK from GitHub Actions
download_github_apk() {
    echo -e "${BLUE}📥 Downloading APK from GitHub Actions...${NC}"
    
    # This would require GitHub CLI or API integration
    echo -e "${YELLOW}⚠️  Manual step required:${NC}"
    echo "1. Go to: https://github.com/YOUR_USERNAME/YOUR_REPO/actions"
    echo "2. Find the latest successful build"
    echo "3. Download the APK artifact"
    echo "4. Save as 'rebyu-github.apk' in this directory"
    echo "5. Run: $0 test"
}

# Function to build with EAS and test
eas_build_and_test() {
    echo -e "${BLUE}🏗️ Building with EAS and testing...${NC}"
    
    cd frontend
    
    # Build with EAS
    echo -e "${YELLOW}🔨 Starting EAS build...${NC}"
    eas build --platform android --profile preview --non-interactive
    
    # EAS will provide download URL
    echo -e "${YELLOW}📥 Download the APK from EAS and save as '../rebyu-eas.apk'${NC}"
    echo "Press Enter when APK is downloaded..."
    read
    
    cd ..
    
    # Test the downloaded APK
    test_eas_apk
}

# Function to setup automated testing
setup_automated_testing() {
    echo -e "${BLUE}🤖 Setting up automated testing...${NC}"
    
    # Create webhook script for EAS
    cat > eas-webhook-handler.sh << 'EOF'
#!/bin/bash

# EAS Build Webhook Handler
# Automatically test APK when EAS build completes

if [ "$1" = "build-complete" ]; then
    APK_URL="$2"
    
    # Download APK
    curl -L "$APK_URL" -o rebyu-eas.apk
    
    # Test on Firebase Test Lab
    ./firebase-cloud-test.sh test
fi
EOF
    
    chmod +x eas-webhook-handler.sh
    
    echo -e "${GREEN}✅ Automated testing setup complete${NC}"
}

# Function to show testing status
show_test_status() {
    echo -e "${BLUE}📊 Firebase Test Lab Status${NC}"
    echo "=========================="
    
    # List recent test results
    $GCLOUD_PATH firebase test android list-results \
        --project "$PROJECT_ID" \
        --limit 5
}

# Function to open Firebase console
open_console() {
    echo -e "${BLUE}🌐 Opening Firebase Test Lab console...${NC}"
    open "https://console.firebase.google.com/project/$PROJECT_ID/testlab"
}

# Main function
main() {
    case "$1" in
        "test")
            test_eas_apk
            ;;
        "download")
            download_github_apk
            ;;
        "build-and-test")
            eas_build_and_test
            ;;
        "setup-auto")
            setup_automated_testing
            ;;
        "status")
            show_test_status
            ;;
        "console")
            open_console
            ;;
        "")
            echo -e "${YELLOW}🔥 Firebase Test Lab Cloud Integration${NC}"
            echo "====================================="
            echo ""
            echo "Usage: $0 [command]"
            echo ""
            echo "Commands:"
            echo "  test           - Test existing APK on Firebase Test Lab"
            echo "  download       - Instructions to download APK from GitHub Actions"
            echo "  build-and-test - Build with EAS and test automatically"
            echo "  setup-auto     - Setup automated testing webhooks"
            echo "  status         - Show recent test results"
            echo "  console        - Open Firebase Test Lab console"
            echo ""
            echo "Examples:"
            echo "  $0 build-and-test    # Build in cloud and test"
            echo "  $0 test              # Test existing APK"
            echo "  $0 console           # View results in browser"
            ;;
        *)
            echo -e "${RED}❌ Unknown command: $1${NC}"
            echo "Use '$0' to see available commands"
            exit 1
            ;;
    esac
}

main "$@"
