#!/bin/bash

# 🔥 Test Rebyu APK en Firebase Test Lab
# =====================================

echo "🔥 Testing Rebyu APK en Firebase Test Lab"
echo "========================================"

# Colors
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m'

# Check if APK exists
APK_FILE="rebyu-app.apk"

if [ ! -f "$APK_FILE" ]; then
    echo -e "${RED}❌ APK no encontrado: $APK_FILE${NC}"
    echo ""
    echo -e "${YELLOW}📥 Pasos para obtener el APK:${NC}"
    echo "1. Ve a: https://github.com/Tobarrientos2/rebyiu/actions"
    echo "2. Haz clic en el build completado (✅)"
    echo "3. Scroll down hasta 'Artifacts'"
    echo "4. <PERSON><PERSON>ga 'rebyu-debug-apk-[hash]'"
    echo "5. Ejecuta estos comandos:"
    echo ""
    echo "   mv ~/Downloads/rebyu-debug-apk-*.zip ."
    echo "   unzip rebyu-debug-apk-*.zip"
    echo "   mv app-debug.apk rebyu-app.apk"
    echo "   ./test-rebyu-apk.sh"
    echo ""
    exit 1
fi

echo -e "${GREEN}✅ APK encontrado: $APK_FILE${NC}"
echo ""

# Show APK info
echo -e "${BLUE}📱 Información del APK:${NC}"
file "$APK_FILE"
ls -lh "$APK_FILE"
echo ""

# Show test configuration
echo -e "${BLUE}🔧 Configuración del Test:${NC}"
echo "• Dispositivo: MediumPhone.arm (Android 30)"
echo "• Tipo: Robo Test (navegación automática)"
echo "• Duración: 5 minutos máximo"
echo "• Proyecto: mobile-testing-1748195029"
echo ""

# Confirm before running
echo -e "${YELLOW}🚀 ¿Ejecutar test en Firebase Test Lab? (y/n):${NC}"
read -r confirm

if [ "$confirm" != "y" ] && [ "$confirm" != "Y" ]; then
    echo "Test cancelado."
    exit 0
fi

echo ""
echo -e "${GREEN}🔥 Iniciando test en Firebase Test Lab...${NC}"
echo "======================================="

# Run the test
/Users/<USER>/google-cloud-sdk/bin/gcloud firebase test android run \
  --type robo \
  --app "$APK_FILE" \
  --device model=MediumPhone.arm,version=30,locale=en,orientation=portrait \
  --timeout 5m \
  --project mobile-testing-1748195029

# Check result
if [ $? -eq 0 ]; then
    echo ""
    echo -e "${GREEN}✅ Test completado exitosamente!${NC}"
    echo ""
    echo -e "${BLUE}📊 Ver resultados detallados:${NC}"
    echo "https://console.firebase.google.com/project/mobile-testing-1748195029/testlab/histories"
    echo ""
    echo -e "${YELLOW}💡 Los resultados incluyen:${NC}"
    echo "• Screenshots de cada pantalla"
    echo "• Logs de la aplicación"
    echo "• Video de la sesión de testing"
    echo "• Reporte de crashes (si los hay)"
    echo "• Métricas de rendimiento"
else
    echo ""
    echo -e "${RED}❌ Test falló. Revisa los logs arriba.${NC}"
    echo ""
    echo -e "${YELLOW}🔧 Posibles soluciones:${NC}"
    echo "• Verificar que el APK sea válido"
    echo "• Revisar permisos de Firebase Test Lab"
    echo "• Intentar con otro dispositivo"
fi

echo ""
echo -e "${BLUE}🎯 Comandos útiles:${NC}"
echo "• Ver historial: gcloud firebase test android list"
echo "• Ver dispositivos: gcloud firebase test android models list"
echo "• Abrir console: open https://console.firebase.google.com/project/mobile-testing-1748195029/testlab"
