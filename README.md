# 🎯 Rebyu - Mobile Learning Roadmaps

Una aplicación web progresiva (PWA) y mobile app para crear y gestionar roadmaps de aprendizaje personalizados con afirmaciones estructuradas en 4 grados de complejidad.

## 🚀 Características

- **📱 Mobile-First**: Optimizada para dispositivos móviles con Capacitor
- **🌐 PWA**: Instalable como app nativa en navegadores
- **🔄 4 Grados de Aprendizaje**: Estructura jerárquica de afirmaciones
- **❓ Preguntas de Reflexión**: 3 preguntas por cada afirmación
- **🎨 UI Moderna**: Diseño responsivo y UX intuitiva
- **🔀 Offline Ready**: Funciona sin conexión (próximamente)

## 🏗️ Arquitectura

### Backend (Puerto 9000)
- **FastAPI** con Python
- **SQLite** para persistencia
- **Docker** para containerización
- **IA Integration** con Gemini/OpenRoute

### Frontend (Puerto 5173)
- **Astro** con build estático
- **Capacitor** para mobile apps
- **Progressive Web App** (PWA)
- **TypeScript** para type safety

## 📱 Plataformas Soportadas

- ✅ **Web Browser** (Chrome, Safari, Firefox)
- ✅ **Android** (vía Capacitor)
- ✅ **iOS** (vía Capacitor - requiere Xcode)
- ✅ **PWA** (instalable desde navegador)

## 🚀 Inicio Rápido

### Prerequisitos
- Docker y Docker Compose
- Node.js 18+ y npm
- (Opcional) Android Studio para desarrollo Android
- (Opcional) Xcode para desarrollo iOS

### 1. Clona el repositorio
```bash
git clone <repository-url>
cd rebyu
```

### 2. Instala dependencias del frontend
```bash
cd frontend
npm install
cd ..
```

### 3. Inicia la aplicación
```bash
# Opción 1: Script automático (recomendado)
./start-dev.sh

# Opción 2: Manual
docker-compose up rebyu-api -d  # Backend en puerto 9000
cd frontend && npm run dev      # Frontend en puerto 5173
```

### 4. Abre la aplicación
- **Web**: http://localhost:5173
- **API**: http://localhost:9000
- **Mobile**: Abre la web en tu móvil y usa "Añadir a pantalla de inicio"

## 📱 Desarrollo Mobile

### Para Android
```bash
cd frontend
npm run build
npx cap sync
npx cap run android  # Requiere Android Studio
```

### Para iOS
```bash
cd frontend
npm run build
npx cap sync
npx cap run ios      # Requiere Xcode
```

### Para PWA
1. Abre http://localhost:5173 en tu móvil
2. Usa "Añadir a pantalla de inicio" en tu navegador
3. La app se instalará como nativa

## 🏢 Estructura del Proyecto

```
rebyu/
├── 📁 backend/
│   ├── main.py           # FastAPI app
│   ├── database.py       # SQLite models
│   ├── schemas.py        # Pydantic schemas
│   └── Dockerfile        # Backend container
├── 📁 frontend/
│   ├── 📁 src/
│   │   ├── 📁 pages/     # Astro pages
│   │   ├── 📁 components/# Astro components
│   │   ├── 📁 layouts/   # Page layouts
│   │   └── 📁 services/  # API services
│   ├── 📁 android/       # Capacitor Android
│   ├── 📁 ios/          # Capacitor iOS
│   ├── capacitor.config.ts # Capacitor config
│   └── public/          # Static assets
├── docker-compose.yml    # Docker services
├── start-dev.sh         # Development script
└── README.md           # Este archivo
```

## 🎓 Estructura de Afirmaciones

La aplicación organiza el aprendizaje en 4 grados:

```
📚 Roadmap: "JavaScript para Principiantes"
├── 🔵 Grado 1: Conceptos Fundamentales
│   ├── ❓ ¿Qué es JavaScript?
│   ├── ❓ ¿Cómo funciona en el navegador?
│   └── ❓ ¿Cuáles son los tipos de datos?
│   └── 🔸 Grado 2: Variables y Funciones
│       ├── ❓ ¿Cómo declarar variables?
│       └── 🔸 Grado 3: Scope y Closures
│           └── 🔸 Grado 4: Implementaciones Avanzadas
└── ... más afirmaciones
```

## 🛠️ Tecnologías Mobile

### Capacitor Plugins Instalados
- **@capacitor/status-bar**: Control de barra de estado
- **@capacitor/splash-screen**: Pantalla de carga
- **@capacitor/keyboard**: Gestión del teclado
- **@capacitor/haptics**: Feedback háptico
- **@capacitor/device**: Información del dispositivo
- **@capacitor/network**: Estado de conectividad

### Características PWA
- **Manifest.json**: Configuración de instalación
- **Service Worker**: Cache y offline (próximamente)
- **Mobile-optimized**: Viewport y touch friendly
- **Theme color**: Integración con sistema operativo

## 🔧 Comandos Útiles

```bash
# Desarrollo
./start-dev.sh                    # Iniciar todo
docker-compose logs rebyu-api     # Ver logs del backend
cd frontend && npm run dev        # Solo frontend

# Build y Deploy
cd frontend && npm run build      # Build estático
npx cap sync                      # Sync mobile apps
npx cap build android            # Build Android
npx cap build ios                # Build iOS

# Debugging
docker-compose exec rebyu-api bash # SSH al container
curl http://localhost:9000/       # Test backend
```

## 🐛 Troubleshooting

### Backend no responde
```bash
docker-compose down
docker-compose up rebyu-api -d
```

### Frontend no carga
```bash
cd frontend
rm -rf node_modules
npm install
npm run dev
```

### Mobile app no funciona
```bash
cd frontend
npm run build
npx cap sync
```

### PWA no se instala
- Verifica que estés en HTTPS o localhost
- Revisa que manifest.json sea válido
- Usa Chrome DevTools > Application > Manifest

## 📋 Próximas Características

- [ ] 🔄 Modo offline completo
- [ ] 🔔 Notificaciones push
- [ ] 👥 Compartir roadmaps
- [ ] 📊 Progreso de aprendizaje
- [ ] 🎨 Temas personalizables
- [ ] 🔍 Búsqueda avanzada
- [ ] 📈 Analytics de uso

## 🤝 Contribución

1. Fork el proyecto
2. Crea una feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit tus cambios (`git commit -m 'Add some AmazingFeature'`)
4. Push a la branch (`git push origin feature/AmazingFeature`)
5. Abre un Pull Request

## 📄 Licencia

Este proyecto está bajo la Licencia MIT - ver el archivo [LICENSE](LICENSE) para más detalles.

## 🆘 Soporte

¿Necesitas ayuda? 
- 📧 Email: <EMAIL>
- 🐛 Issues: GitHub Issues
- 📚 Docs: Wiki del proyecto

---

**🎯 Rebyu** - Transformando el aprendizaje, una afirmación a la vez.
