-- SQL statements to import data from SQLite to Neon DB
-- Execute these statements on your Neon DB

-- Classes

-- Roadmaps
INSERT INTO roadmaps (title, description, topic) VALUES ('Roadmap sobre Inteligencia Artificial', 'Un roadmap completo para aprender IA desde cero', 'Inteligencia Artificial');
INSERT INTO roadmaps (title, description, topic) VALUES ('Roadmap sobre Svelte ', 'do svelte', 'Svelte ');
INSERT INTO roadmaps (title, description, topic) VALUES ('Roadmap sobre React', 'I want to learn react because it''s cool', 'React');
INSERT INTO roadmaps (title, description, topic) VALUES ('Roadmap sobre Inteligencia Artificial', 'Un roadmap completo para aprender IA desde cero', 'Inteligencia Artificial');
INSERT INTO roadmaps (title, description, topic) VALUES ('Roadmap sobre Svelte ', 'do svelte', 'Svelte ');
INSERT INTO roadmaps (title, description, topic) VALUES ('Roadmap sobre Inteligencia Artificial', 'Un roadmap completo para aprender IA desde cero', 'Inteligencia Artificial');
INSERT INTO roadmaps (title, description, topic) VALUES ('Roadmap sobre Svelte ', 'do svelte', 'Svelte ');
INSERT INTO roadmaps (title, description, topic) VALUES ('Roadmap sobre Inteligencia Artificial', 'Un roadmap completo para aprender IA desde cero', 'Inteligencia Artificial');
INSERT INTO roadmaps (title, description, topic) VALUES ('Roadmap sobre Svelte ', 'do svelte', 'Svelte ');

-- Questions (Grade 1)
-- Original Question ID: 1
INSERT INTO questions (content, grade, roadmap_id, parent_id) VALUES ('¿Qué es la Inteligencia Artificial?', 1, (SELECT id FROM roadmaps WHERE title = (SELECT title FROM roadmaps WHERE id = 1 LIMIT 1) LIMIT 1), NULL);

-- Subquestions (Grade 2)
-- Original Subquestion ID: 1
INSERT INTO questions (content, grade, roadmap_id, parent_id) VALUES ('¿Cuál es la definición formal de Inteligencia Artificial?', 2, NULL, (SELECT id FROM questions WHERE content = '¿Qué es la Inteligencia Artificial?' AND grade = 1 LIMIT 1));

-- Subquestions (Grade 3)
-- Original Subquestion ID: 2
INSERT INTO questions (content, grade, roadmap_id, parent_id) VALUES ('¿Cómo se relaciona la IA con el aprendizaje automático?', 3, NULL, (SELECT id FROM questions WHERE content = '¿Cuál es la definición formal de Inteligencia Artificial?' AND grade = 2 LIMIT 1));
