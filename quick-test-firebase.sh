#!/bin/bash

# 🔥 Quick Firebase Test Lab Demo
# ===============================

GCLOUD_PATH="/Users/<USER>/google-cloud-sdk/bin/gcloud"
PROJECT_ID="mobile-testing-1748195029"

echo "🔥 Firebase Test Lab - Demo Rápido"
echo "=================================="

# Create a simple test APK using Android SDK tools
create_simple_apk() {
    echo "📱 Creando APK simple para demo..."
    
    # Create a minimal Android project structure
    mkdir -p simple-app/src/main/java/com/demo/app
    mkdir -p simple-app/src/main/res/values
    mkdir -p simple-app/src/main/res/layout
    
    # Create AndroidManifest.xml
    cat > simple-app/src/main/AndroidManifest.xml << 'EOF'
<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.demo.app"
    android:versionCode="1"
    android:versionName="1.0">
    
    <uses-sdk android:minSdkVersion="21" android:targetSdkVersion="30" />
    
    <application
        android:label="Demo App"
        android:theme="@android:style/Theme.Material.Light">
        <activity
            android:name=".MainActivity"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
    </application>
</manifest>
EOF

    # Create MainActivity.java
    cat > simple-app/src/main/java/com/demo/app/MainActivity.java << 'EOF'
package com.demo.app;

import android.app.Activity;
import android.os.Bundle;
import android.widget.TextView;

public class MainActivity extends Activity {
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        TextView tv = new TextView(this);
        tv.setText("¡Hola Firebase Test Lab!");
        tv.setTextSize(24);
        tv.setPadding(50, 50, 50, 50);
        setContentView(tv);
    }
}
EOF

    # Create strings.xml
    cat > simple-app/src/main/res/values/strings.xml << 'EOF'
<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="app_name">Demo App</string>
</resources>
EOF

    echo "✅ Estructura de app creada"
}

# Build APK using aapt and dx tools
build_simple_apk() {
    echo "🔨 Construyendo APK..."
    
    cd simple-app
    
    # Set Android SDK paths
    ANDROID_HOME="/Users/<USER>/Library/Android/sdk"
    BUILD_TOOLS="$ANDROID_HOME/build-tools/34.0.0"
    PLATFORM="$ANDROID_HOME/platforms/android-30"
    
    # Check if build tools exist
    if [ ! -d "$BUILD_TOOLS" ]; then
        echo "❌ Build tools no encontrados en $BUILD_TOOLS"
        echo "📋 Build tools disponibles:"
        ls "$ANDROID_HOME/build-tools/" 2>/dev/null || echo "No hay build tools instalados"
        return 1
    fi
    
    # Create build directory
    mkdir -p build/gen build/obj build/apk
    
    # Generate R.java
    echo "📝 Generando R.java..."
    "$BUILD_TOOLS/aapt" package -f -m \
        -J build/gen \
        -M src/main/AndroidManifest.xml \
        -S src/main/res \
        -I "$PLATFORM/android.jar"
    
    # Compile Java files
    echo "☕ Compilando Java..."
    javac -d build/obj \
        -classpath "$PLATFORM/android.jar" \
        -sourcepath src/main/java:build/gen \
        src/main/java/com/demo/app/MainActivity.java \
        build/gen/com/demo/app/R.java
    
    # Create DEX file
    echo "📦 Creando DEX..."
    "$BUILD_TOOLS/dx" --dex \
        --output=build/apk/classes.dex \
        build/obj
    
    # Package resources
    echo "📋 Empaquetando recursos..."
    "$BUILD_TOOLS/aapt" package -f \
        -M src/main/AndroidManifest.xml \
        -S src/main/res \
        -I "$PLATFORM/android.jar" \
        -F build/apk/demo.unsigned.apk \
        build/apk
    
    # Add DEX to APK
    cd build/apk
    "$BUILD_TOOLS/aapt" add demo.unsigned.apk classes.dex
    
    # Sign APK (debug keystore)
    echo "🔐 Firmando APK..."
    if [ ! -f ~/.android/debug.keystore ]; then
        mkdir -p ~/.android
        keytool -genkey -v \
            -keystore ~/.android/debug.keystore \
            -storepass android \
            -alias androiddebugkey \
            -keypass android \
            -keyalg RSA \
            -keysize 2048 \
            -validity 10000 \
            -dname "CN=Android Debug,O=Android,C=US"
    fi
    
    jarsigner -verbose \
        -keystore ~/.android/debug.keystore \
        -storepass android \
        -keypass android \
        demo.unsigned.apk \
        androiddebugkey
    
    # Align APK
    "$BUILD_TOOLS/zipalign" -f 4 demo.unsigned.apk demo-signed.apk
    
    # Copy to project root
    cp demo-signed.apk ../../../demo-app.apk
    cd ../../..
    
    echo "✅ APK creado: demo-app.apk"
}

# Test with Firebase Test Lab
test_with_firebase() {
    echo ""
    echo "🔥 Probando con Firebase Test Lab"
    echo "================================="
    
    APK_FILE="demo-app.apk"
    
    if [ ! -f "$APK_FILE" ]; then
        echo "❌ APK no encontrado: $APK_FILE"
        return 1
    fi
    
    echo "📱 Información del test:"
    echo "  APK: $APK_FILE"
    echo "  Proyecto: $PROJECT_ID"
    echo "  Dispositivo: MediumPhone.arm (Android 30)"
    echo ""
    
    echo "🚀 Ejecutando test..."
    
    $GCLOUD_PATH firebase test android run \
        --type robo \
        --app "$APK_FILE" \
        --device model=MediumPhone.arm,version=30,locale=en,orientation=portrait \
        --timeout 3m \
        --project "$PROJECT_ID"
}

# Show results
show_results() {
    echo ""
    echo "📊 RESULTADOS"
    echo "============"
    echo "🌐 Ver resultados completos en:"
    echo "  https://console.firebase.google.com/project/$PROJECT_ID/testlab"
    echo ""
    echo "📁 Los resultados incluyen:"
    echo "  ✅ Screenshots automáticos"
    echo "  ✅ Video de la sesión"
    echo "  ✅ Logs de la aplicación"
    echo "  ✅ Reporte de rendimiento"
    echo ""
}

# Main execution
main() {
    case "$1" in
        "build")
            create_simple_apk
            build_simple_apk
            ;;
        "test")
            test_with_firebase
            show_results
            ;;
        "full"|"")
            create_simple_apk
            build_simple_apk
            test_with_firebase
            show_results
            ;;
        *)
            echo "Uso: $0 [build|test|full]"
            echo ""
            echo "  build - Solo crear APK"
            echo "  test  - Solo probar (APK debe existir)"
            echo "  full  - Crear APK y probar (default)"
            ;;
    esac
}

main "$@"
