#!/bin/bash

# 🚀 EAS Build Setup para Rebyu (Alternativa a Gradle local)
# =========================================================

echo "🚀 Configurando EAS Build para Rebyu"
echo "===================================="

# Instalar EAS CLI
echo "📦 Instalando EAS CLI..."
npm install -g @expo/eas-cli

# Verificar instalación
echo "✅ Verificando EAS CLI..."
eas --version

echo ""
echo "📋 PASOS PARA USAR EAS BUILD:"
echo "============================"
echo ""
echo "1️⃣  Inicializar EAS en tu proyecto:"
echo "   cd /Users/<USER>/Development/rebyiu/frontend"
echo "   eas build:configure"
echo ""
echo "2️⃣  Crear cuenta Expo (gratis):"
echo "   eas login"
echo ""
echo "3️⃣  Construir APK en la nube:"
echo "   eas build --platform android --profile preview"
echo ""
echo "4️⃣  Descargar APK cuando esté listo:"
echo "   # EAS te dará un link de descarga"
echo ""
echo "💡 VENTAJAS DE EAS BUILD:"
echo "========================"
echo "✅ No necesitas configurar Gradle localmente"
echo "✅ Build en servidores de Expo (siempre actualizados)"
echo "✅ Compatible con Capacitor"
echo "✅ Gratis para proyectos pequeños"
echo "✅ APK listo para Firebase Test Lab"

echo ""
echo "🔧 CONFIGURACIÓN NECESARIA:"
echo "=========================="
echo "Crear archivo: frontend/eas.json"

cat > /Users/<USER>/Development/rebyiu/frontend/eas.json << 'EOF'
{
  "cli": {
    "version": ">= 5.0.0"
  },
  "build": {
    "development": {
      "developmentClient": true,
      "distribution": "internal"
    },
    "preview": {
      "android": {
        "buildType": "apk"
      }
    },
    "production": {}
  },
  "submit": {
    "production": {}
  }
}
EOF

echo "✅ Archivo eas.json creado"
echo ""
echo "🚀 COMANDOS PARA EJECUTAR:"
echo "========================="
echo "cd /Users/<USER>/Development/rebyiu/frontend"
echo "eas login"
echo "eas build --platform android --profile preview"
